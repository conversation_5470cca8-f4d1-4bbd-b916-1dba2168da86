<?php
/**
 * ملف الرأس المشترك لجميع صفحات لوحة التحكم
 * Common Header for all Dashboard Pages
 */

// التحقق من تسجيل الدخول
checkLogin();

// الحصول على بيانات المستخدم الحالي
$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'لوحة التحكم'; ?></title>

    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #2c3e50;
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f4f6f9;
            font-size: 14px;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            transition: all 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h4 {
            margin: 0;
            font-weight: 700;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .sidebar-header h4 {
            opacity: 0;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .sidebar-menu .menu-item {
            margin: 0.2rem 0;
        }

        .sidebar-menu .menu-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar-menu .menu-link:hover,
        .sidebar-menu .menu-link.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-right-color: var(--secondary-color);
        }

        .sidebar-menu .menu-link i {
            width: 20px;
            text-align: center;
            margin-left: 1rem;
            font-size: 1.1rem;
        }

        .sidebar-menu .menu-text {
            flex: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .menu-text {
            opacity: 0;
        }

        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            transition: margin-right 0.3s ease;
            min-height: 100vh;
        }

        .sidebar.collapsed + .main-content {
            margin-right: 70px;
        }

        /* Top Bar */
        .topbar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }

        .topbar-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--secondary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Cards */
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: none;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .stat-label {
            color: #6c757d;
            margin: 0;
        }

        /* Tables */
        .table-container {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .table th {
            background: var(--light-color);
            border: none;
            font-weight: 600;
            color: var(--primary-color);
        }

        .table td {
            border: none;
            vertical-align: middle;
            border-bottom: 1px solid #f1f3f4;
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-success {
            background: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-warning {
            background: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-danger {
            background: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        /* Forms */
        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        /* Alerts */
        .alert {
            border-radius: 8px;
            border: none;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .topbar {
                padding: 1rem;
            }
        }

        /* Loading Spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Custom Scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 5px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 5px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* توسيط عناوين جميع الجداول في النظام */
        table th,
        .table th,
        .table-dark th,
        .table thead th,
        thead th,
        th {
            text-align: center !important;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <h4><i class="fas fa-car me-2"></i>لوحة التحكم</h4>
        </div>

        <!-- Sidebar Menu -->
        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>dashboard.php" class="menu-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="menu-text">الرئيسية</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>pages/cars/index.php" class="menu-link <?php echo (strpos($_SERVER['PHP_SELF'], '/cars/') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-car"></i>
                    <span class="menu-text">إدارة السيارات</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>pages/categories/index.php" class="menu-link <?php echo (strpos($_SERVER['PHP_SELF'], '/categories/') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-tags"></i>
                    <span class="menu-text">فئات السيارات</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>analytics.php" class="menu-link <?php echo (basename($_SERVER['PHP_SELF']) == 'analytics.php') ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i>
                    <span class="menu-text">الإحصائيات</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>reviews.php" class="menu-link <?php echo (basename($_SERVER['PHP_SELF']) == 'reviews.php') ? 'active' : ''; ?>">
                    <i class="fas fa-comments"></i>
                    <span class="menu-text">إدارة الآراء</span>
                </a>
            </div>

            <?php if ($currentUser['is_admin'] == 1): ?>
            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>pages/users/index.php" class="menu-link <?php echo (strpos($_SERVER['PHP_SELF'], '/users/') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>pages/settings/index.php" class="menu-link <?php echo (strpos($_SERVER['PHP_SELF'], '/settings/') !== false) ? 'active' : ''; ?>">
                    <i class="fas fa-cog"></i>
                    <span class="menu-text">إعدادات الموقع</span>
                </a>
            </div>
            <?php endif; ?>

            <div class="menu-item">
                <a href="<?php echo BASE_URL; ?>auth/logout.php" class="menu-link" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn btn-link me-3 d-md-none" id="toggleSidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="topbar-title"><?php echo $pageTitle ?? 'لوحة التحكم'; ?></h1>
            </div>

            <div class="user-menu">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($currentUser['fullname'] ?? 'A', 0, 1)); ?>
                </div>
                <div class="d-none d-md-block">
                    <div class="fw-bold"><?php echo htmlspecialchars($currentUser['fullname'] ?? ''); ?></div>
                    <small class="text-muted">
                        <?php echo ($currentUser['is_admin'] == 1) ? 'مدير' : 'مستخدم'; ?>
                    </small>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="container-fluid px-4">