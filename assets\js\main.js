window.addEventListener('load', function () {
  setTimeout(function () {
    const preloader = document.querySelector('.preloader');
    preloader.style.opacity = '0';

    setTimeout(function () {
      preloader.style.display = 'none';
    }, 500);
  }, 1000);
});
document.addEventListener('DOMContentLoaded', function () {
  'use strict';

  const navbar = document.querySelector('.navbar');

  if (navbar) {
    window.addEventListener('scroll', function () {
      if (window.scrollY > 50) {
        navbar.classList.add('shadow');
        navbar.classList.add('py-0');
        navbar.classList.add('navbar-scrolled');

        const logo = navbar.querySelector('.navbar-brand img');
        if (logo) {
          logo.style.maxHeight = '40px';
        }
      } else {
        navbar.classList.remove('shadow');
        navbar.classList.remove('py-0');
        navbar.classList.remove('navbar-scrolled');

        const logo = navbar.querySelector('.navbar-brand img');
        if (logo) {
          logo.style.maxHeight = '50px';
        }
      }
    });
  }

  const heroSection = document.querySelector('.hero-section');

  if (heroSection) {
    const shapes = document.querySelectorAll('.shape-1, .shape-2, .shape-3');

    window.addEventListener('mousemove', function (e) {
      const x = e.clientX / window.innerWidth;
      const y = e.clientY / window.innerHeight;

      shapes.forEach(function (shape, index) {
        const speed = (index + 1) * 20;
        const moveX = (x - 0.5) * speed;
        const moveY = (y - 0.5) * speed;

        shape.style.transform = `translate(${moveX}px, ${moveY}px)`;
      });
    });
  }

  const heroImage = document.querySelector('.hero-image');

  if (heroImage) {
    heroSection.addEventListener('mousemove', function (e) {
      const x = e.clientX / window.innerWidth - 0.5;
      const y = e.clientY / window.innerHeight - 0.5;

      heroImage.querySelector('img').style.transform = `rotateY(${
        -x * 10
      }deg) rotateX(${y * 10}deg)`;
    });

    heroSection.addEventListener('mouseleave', function () {
      heroImage.querySelector('img').style.transform =
        'rotateY(-5deg) rotateX(5deg)';
    });
  }

  const navLinks = document.querySelectorAll('.nav-link');
  const menuToggle = document.getElementById('navbarNav');

  if (menuToggle) {
    const bsCollapse = new bootstrap.Collapse(menuToggle, { toggle: false });

    navLinks.forEach(function (link) {
      link.addEventListener('click', function () {
        if (window.innerWidth < 992 && menuToggle.classList.contains('show')) {
          bsCollapse.toggle();
        }
      });
    });
  }

  if (!window.AOS) {
    const animatedElements = document.querySelectorAll('[data-aos]');

    const checkIfInView = function () {
      animatedElements.forEach(function (element) {
        const elementTop = element.getBoundingClientRect().top;
        const elementBottom = element.getBoundingClientRect().bottom;

        const isInView =
          elementTop < window.innerHeight - 100 && elementBottom > 0;

        if (isInView && !element.classList.contains('aos-animated')) {
          element.classList.add('aos-animated');
        }
      });
    };

    window.addEventListener('scroll', checkIfInView);
    checkIfInView();
  }

  const alerts = document.querySelectorAll('.alert-box');

  alerts.forEach(function (alert) {
    setTimeout(function () {
      alert.style.opacity = '0';
      alert.style.transform = 'translateY(-20px)';

      setTimeout(function () {
        alert.remove();
      }, 500);
    }, 5000);

    const closeBtn = alert.querySelector('.close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', function () {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';

        setTimeout(function () {
          alert.remove();
        }, 500);
      });
    }
  });

  const filterButtons = document.querySelectorAll('.filter-buttons .btn');
  const carItems = document.querySelectorAll('.car-card');

  filterButtons.forEach(function (button) {
    button.addEventListener('click', function (e) {
      e.preventDefault();

      const category = this.getAttribute('data-filter');

      filterButtons.forEach(function (btn) {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
      });

      this.classList.remove('btn-outline-primary');
      this.classList.add('btn-primary');

      if (category === 'all') {
        carItems.forEach(function (item) {
          item.style.opacity = '0';
          setTimeout(function () {
            item.style.display = 'block';
            setTimeout(function () {
              item.style.opacity = '1';
            }, 50);
          }, 300);
        });
      } else {
        carItems.forEach(function (item) {
          if (item.getAttribute('data-category') === category) {
            item.style.opacity = '0';
            setTimeout(function () {
              item.style.display = 'block';
              setTimeout(function () {
                item.style.opacity = '1';
              }, 50);
            }, 300);
          } else {
            item.style.opacity = '0';
            setTimeout(function () {
              item.style.display = 'none';
            }, 300);
          }
        });
      }
    });
  });

  const scrollLinks = document.querySelectorAll('a[href^="#"]');

  scrollLinks.forEach(function (link) {
    link.addEventListener('click', function (e) {
      const targetId = this.getAttribute('href');

      if (targetId !== '#') {
        e.preventDefault();

        const targetSection = document.querySelector(targetId);

        if (targetSection) {
          const headerHeight = document.querySelector('header')
            ? document.querySelector('header').offsetHeight
            : 80;

          const highlightSection = function () {
            targetSection.classList.add('section-highlight');
            setTimeout(function () {
              targetSection.classList.remove('section-highlight');
            }, 1000);
          };

          window.scrollTo({
            top: targetSection.offsetTop - headerHeight,
            behavior: 'smooth',
          });

          setTimeout(highlightSection, 800);
        }
      }
    });
  });

  const cards = document.querySelectorAll(
    '.service-card, .car-card, .contact-info-card'
  );

  cards.forEach(function (card) {
    card.addEventListener('mousemove', function (e) {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      const rotateX = (y - centerY) / 25;
      const rotateY = (centerX - x) / 25;

      card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`;
    });

    card.addEventListener('mouseleave', function () {
      card.style.transform =
        'perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)';
    });
  });

  const contactForm = document.getElementById('contactForm');

  if (contactForm) {
    const formInputs = contactForm.querySelectorAll('.form-control');

    formInputs.forEach(function (input) {
      input.addEventListener('focus', function () {
        this.parentElement.classList.add('input-focus');
      });

      input.addEventListener('blur', function () {
        this.parentElement.classList.remove('input-focus');
      });
    });

    contactForm.addEventListener('submit', function (e) {
      let isValid = true;
      const name = document.getElementById('name');
      const email = document.getElementById('email');
      const phone = document.getElementById('phone');
      const message = document.getElementById('message');

      if (name && name.value.trim() === '') {
        isValid = false;
        name.classList.add('is-invalid');

        name.parentElement.classList.add('shake-input');
        setTimeout(function () {
          name.parentElement.classList.remove('shake-input');
        }, 500);
      } else if (name) {
        name.classList.remove('is-invalid');
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (email && !emailRegex.test(email.value.trim())) {
        isValid = false;
        email.classList.add('is-invalid');

        email.parentElement.classList.add('shake-input');
        setTimeout(function () {
          email.parentElement.classList.remove('shake-input');
        }, 500);
      } else if (email) {
        email.classList.remove('is-invalid');
      }

      if (phone && phone.value.trim() === '') {
        isValid = false;
        phone.classList.add('is-invalid');

        phone.parentElement.classList.add('shake-input');
        setTimeout(function () {
          phone.parentElement.classList.remove('shake-input');
        }, 500);
      } else if (phone) {
        phone.classList.remove('is-invalid');
      }

      if (message && message.value.trim() === '') {
        isValid = false;
        message.classList.add('is-invalid');

        message.parentElement.classList.add('shake-input');
        setTimeout(function () {
          message.parentElement.classList.remove('shake-input');
        }, 500);
      } else if (message) {
        message.classList.remove('is-invalid');
      }

      if (!isValid) {
        e.preventDefault();

        const firstInvalidInput = contactForm.querySelector('.is-invalid');
        if (firstInvalidInput) {
          firstInvalidInput.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      } else {
        const submitBtn = contactForm.querySelector('button[type="submit"]');
        if (submitBtn) {
          submitBtn.innerHTML =
            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...';
          submitBtn.disabled = true;
        }
      }
    });
  }

  const carImages = document.querySelectorAll('.car-img img');

  carImages.forEach(function (image) {
    image.addEventListener('click', function () {
      const overlay = document.createElement('div');
      overlay.classList.add('image-zoom-overlay');
      document.body.appendChild(overlay);

      const zoomContainer = document.createElement('div');
      zoomContainer.classList.add('image-zoom-container');

      const zoomedImage = document.createElement('img');
      zoomedImage.src = this.src;
      zoomedImage.classList.add('zoomed-image');

      const closeButton = document.createElement('button');
      closeButton.classList.add('zoom-close-btn');
      closeButton.innerHTML = '&times;';

      zoomContainer.appendChild(zoomedImage);
      zoomContainer.appendChild(closeButton);
      document.body.appendChild(zoomContainer);

      setTimeout(function () {
        overlay.classList.add('active');
        zoomContainer.classList.add('active');
      }, 10);

      const closeZoom = function () {
        overlay.classList.remove('active');
        zoomContainer.classList.remove('active');

        setTimeout(function () {
          overlay.remove();
          zoomContainer.remove();
        }, 300);
      };

      closeButton.addEventListener('click', closeZoom);
      overlay.addEventListener('click', closeZoom);
    });
  });
});
lightbox.option({
  resizeDuration: 200,
  wrapAround: true,
  albumLabel: 'صورة %1 من %2',
});
