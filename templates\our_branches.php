<?php
$currentLang = getCurrentLanguage();
?>

<!-- Contact Hero Section -->
<section class="hero-section position-relative text-center" style="padding-top: 100px;">

    <div class="animated-bg" data-url="<?php echo ASSETS_PATH; ?>" style=" background-image: url('<?php echo ASSETS_PATH; ?>images/k4-25my-kv-pc.jpg');"></div>
    <div class="container" style="z-index: 2;">
        <div class="row min-vh-80 align-items-center">
            <div class="text-white flex flex-col gap-5 pb-5 w-full position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <h1 class="display-4 fw-bold mb-3 text-white animate__animated animate__fadeInDown" style="text-transform: uppercase; letter-spacing: 1.5px;"><?php echo $lang['menu_branches']; ?></h1>
                <div class="divider mx-auto mb-4" style="height: 4px; width: 70px; background-color: #05141f;"></div>
                <div class="text-rotator">
                    <p class="lead mb-4 text-white rotating-text active"><?php echo $lang['hero_subtitle1']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle2']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle3']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle4']; ?></p>
                </div>
            </div>
        </div>
    </div>
    <svg data-name="VIDEO GRADIENT" xmlns="http://www.w3.org/2000/svg" class="home-hero__gradient" role="presentation">
        <defs data-v-1bfe21c6="">
            <linearGradient id="DesktopGradient_svg__a" x1=".5" y1=".129" x2=".5" y2=".708" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-opacity="0"></stop>
                <stop offset=".364" stop-opacity=".424"></stop>
                <stop offset=".64" stop-color="#030b11" stop-opacity=".733"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
            <linearGradient id="DesktopGradient_svg__b" x1=".5" y1=".129" x2=".5" y2=".5" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-color="#05141f" stop-opacity="0"></stop>
                <stop offset=".226" stop-color="#05141f" stop-opacity=".082"></stop>
                <stop offset=".512" stop-color="#05141f" stop-opacity=".271"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
        </defs>
        <rect fill="url(#DesktopGradient_svg__a)" width="100%" height="204"></rect>
        <rect fill="url(#DesktopGradient_svg__b)" y="72" width="100%" height="132"></rect>
    </svg>
</section>

<!-- Contact Form & Info Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Information -->
            <div class="col-lg-12" data-aos="fade-up" data-aos-delay="200">
                <div class="contact-info bg-primary text-white p-4 p-md-5 rounded shadow h-100">
                    <h3 class="mb-4 border-bottom pb-3 text-white"><?php echo $lang['footer_contact']; ?></h3>

                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0">
                            <div class="contact-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 50px; height: 50px;">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                            </div>
                        </div>
                        <div class="ms-3">
                            <h5 class="text-white"><?php echo $lang['address']; ?></h5>
                            <p class="mb-0"><?php echo ($currentLang == 'ar') ? COMPANY_ADDRESS_AR : COMPANY_ADDRESS_EN; ?></p>
                            <p><i class="fas fa-phone-alt"></i> <span>07706000067</span> - <span>07808670779</span></p>
                        </div>
                    </div>

                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0">
                            <div class="contact-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 50px; height: 50px;">
                                <i class="fas fa-clock text-primary"></i>
                            </div>
                        </div>
                        <div class="ms-3">
                            <h5 class="text-white"><?php echo $lang['working_hours']; ?></h5>
                            <p class="mb-1"><?php echo $lang['working_hours_value']; ?></p>
                            <p class="mb-0"><?php echo $lang['friday_hours']; ?></p>
                        </div>
                    </div>

                    <div class="branches-section mb-4">
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                <div class="contact-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 50px; height: 50px;">
                                    <i class="fas fa-phone-alt text-primary"></i>
                                </div>
                            </div>
                            <div class="ms-3">
                                <h5 class="text-white mb-3"><?php echo $lang['branches']; ?></h5>

                                <div class="branch-item mb-3">
                                    <h6 class="text-white mb-1"><?php echo $lang['international_branch']; ?></h6>
                                    <p class="mb-1"><i class="fas fa-phone-alt"></i> 07901219737</p>
                                </div>

                                <div class="branch-item mb-3">
                                    <h6 class="text-white mb-1"><?php echo $lang['nineveh_branch']; ?></h6>
                                    <p class="mb-1"><?php echo $lang['ninveh_branch_address']; ?></p>
                                    <p class="mb-0"><i class="fas fa-phone-alt"></i> 07704466455</p>
                                </div>

                                <div class="branch-item mb-3">
                                    <h6 class="text-white mb-1"><?php echo $lang['diyala_branch']; ?></h6>
                                    <p class="mb-1"><?php echo $lang['diyala_branch_address']; ?></p>
                                    <p class="mb-0"><i class="fas fa-phone-alt"></i> 07710972739</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0">
                            <div class="contact-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 50px; height: 50px;">
                                <i class="fas fa-envelope text-primary"></i>
                            </div>
                        </div>
                        <div class="ms-3">
                            <h5 class="text-white"><?php echo $lang['email']; ?></h5>
                            <p class="mb-0"><?php echo COMPANY_EMAIL; ?></p>
                        </div>
                    </div>

                    <div class="mt-5">
                        <h5 class="text-white mb-3"><?php echo $lang['follow_us']; ?></h5>
                        <div class="d-flex">
                            <a href="<?php echo FACEBOOK_URL; ?>" class="me-3" target="_blank">
                                <div class="social-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 40px; height: 40px;">
                                    <i class="fab fa-facebook-f text-primary"></i>
                                </div>
                            </a>
                            <a href="<?php echo INSTAGRAM_URL; ?>" class="me-3" target="_blank">
                                <div class="social-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 40px; height: 40px;">
                                    <i class="fab fa-instagram text-primary"></i>
                                </div>
                            </a>
                            <a href="<?php echo YOUTUBE_URL; ?>" class="me-3" target="_blank">
                                <div class="social-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 40px; height: 40px;">
                                    <i class="fab fa-youtube text-primary"></i>
                                </div>
                            </a>
                            <a href="<?php echo TIKTOK_URL; ?>" class="me-3" target="_blank">
                                <div class="social-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 40px; height: 40px;">
                                    <i class="fab fa-tiktok text-primary"></i>
                                </div>
                            </a>
                            <a href="<?php echo LINKEDIN_URL; ?>" class="me-3" target="_blank">
                                <div class="social-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 40px; height: 40px;">
                                    <i class="fab fa-linkedin-in text-primary"></i>
                                </div>
                            </a>
                            <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank">
                                <div class="social-icon-box d-flex align-items-center justify-content-center bg-white rounded-circle" style="width: 40px; height: 40px;">
                                    <i class="fab fa-whatsapp text-primary"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const texts = document.querySelectorAll('.rotating-text');
        let currentIndex = 0;
        const intervalTime = 10000;

        function rotateText() {
            const currentActive = document.querySelector('.rotating-text.active');
            if (currentActive) {
                currentActive.classList.add('fade-out');
                currentActive.classList.remove('active');
            }

            currentIndex = (currentIndex + 1) % texts.length;

            setTimeout(() => {
                texts[currentIndex].classList.remove('fade-out');
                texts[currentIndex].classList.add('active');
            }, 300);
        }

        let rotationInterval = setInterval(rotateText, intervalTime);

    });
</script>