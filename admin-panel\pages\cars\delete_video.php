<?php
/**
 * حذف فيديو السيارة
 * Delete Car Video
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// التحقق من أن الطلب من نوع POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.0 405 Method Not Allowed');
    exit;
}

// التحقق من وجود معرف الفيديو
if (!isset($_POST['video_id']) || !is_numeric($_POST['video_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الفيديو غير صحيح']);
    exit;
}

// التحقق من رمز الأمان
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
    exit;
}

$videoId = intval($_POST['video_id']);
$carId = 0;

try {
    // استرجاع بيانات الفيديو
    $video = $db->Fetch("SELECT video_url, car_id FROM car_videos WHERE video_id = :video_id", ['video_id' => $videoId]);

    if (!$video) {
        echo json_encode(['success' => false, 'message' => 'الفيديو غير موجود']);
        exit;
    }

    // التحقق من صلاحية المستخدم (يمكن إضافة المزيد من checks حسب احتياجات النظام)
    // مثلاً التحقق من أن السيارة تابعة للمستخدم الحالي

    $carId = $video['car_id'];

    // حذف ملف الفيديو من الخادم
    $videoPath = UPLOAD_PATH . $video['video_url'];
    if (file_exists($videoPath)) {
        unlink($videoPath);
    }

    // حذف الفيديو من قاعدة البيانات
    $db->Remove("DELETE FROM car_videos WHERE video_id = :video_id", ['video_id' => $videoId]);

    echo json_encode(['success' => true, 'message' => 'تم حذف الفيديو بنجاح']);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حذف الفيديو: ' . $e->getMessage()]);
}
