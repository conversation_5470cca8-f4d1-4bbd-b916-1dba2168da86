    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white pt-5 pb-3">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5 class="mb-3 fw-bold"><?php echo $lang['footer_about']; ?></h5>
                    <p style="text-align: justify;"><?php echo $lang['footer_about_content']; ?></p>

                    <h6 class="mb-2 mt-4"><?php echo $lang['follow_us']; ?></h6>
                    <div class="social-icons">
                        <a href="<?php echo FACEBOOK_URL; ?>" class="text-white me-3" target="_blank"><i class="fab fa-facebook-f fa-lg"></i></a>
                        <a href="<?php echo INSTAGRAM_URL; ?>" class="text-white me-3" target="_blank"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="<?php echo YOUTUBE_URL; ?>" class="text-white me-3" target="_blank"><i class="fab fa-youtube fa-lg"></i></a>
                        <a href="<?php echo TIKTOK_URL; ?>" class="text-white me-3" target="_blank"><i class="fab fa-tiktok fa-lg"></i></a>
                        <a href="<?php echo LINKEDIN_URL; ?>" class="text-white me-3" target="_blank"><i class="fab fa-linkedin-in fa-lg"></i></a>
                        <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" class="text-white me-3" target="_blank"><i class="fab fa-whatsapp fa-lg"></i></a>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <h5 class="mb-3 fw-bold"><?php echo $lang['footer_links']; ?></h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="<?php echo createLink('index.php'); ?>" class="text-decoration-none text-white">
                                <i class="fas fa-chevron-right me-1 text-primary small"></i> <?php echo $lang['menu_home']; ?>
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="<?php echo createLink('cars.php'); ?>" class="text-decoration-none text-white">
                                <i class="fas fa-chevron-right me-1 text-primary small"></i> <?php echo $lang['menu_cars']; ?>
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="<?php echo createLink('index.php'); ?>#services" class="text-decoration-none text-white">
                                <i class="fas fa-chevron-right me-1 text-primary small"></i> <?php echo $lang['menu_services']; ?>
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="<?php echo createLink('about-us.php'); ?>" class="text-decoration-none text-white">
                                <i class="fas fa-chevron-right me-1 text-primary small"></i> <?php echo $lang['menu_about']; ?>
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="<?php echo createLink('our_branches.php'); ?>" class="text-decoration-none text-white">
                                <i class="fas fa-chevron-right me-1 text-primary small"></i> <?php echo $lang['menu_branches']; ?>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo createLink('contact.php'); ?>" class="text-decoration-none text-white">
                                <i class="fas fa-chevron-right me-1 text-primary small"></i> <?php echo $lang['menu_contact']; ?>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="col-md-4 mb-4">
                    <h5 class="mb-3 fw-bold"><?php echo $lang['footer_contact']; ?></h5>
                    <p class="mb-2">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <?php echo ($currentLang == 'ar') ? COMPANY_ADDRESS_AR : COMPANY_ADDRESS_EN; ?>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-phone-alt me-2 text-primary"></i>
                        <?php echo $lang['customer_service'] . ' - ' . COMPANY_PHONE; ?>
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <?php echo COMPANY_EMAIL; ?>
                    </p>
                    <p class="mb-2">
                        <i class="far fa-clock me-2 text-primary"></i>
                        <?php echo $lang['working_hours_value']; ?>
                    </p>
                    <p>
                        <i class="far fa-clock me-2 text-primary"></i>
                        <?php echo $lang['friday_hours']; ?>
                    </p>
                </div>
            </div>

            <!-- Google Map -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="map-container rounded overflow-hidden shadow">
                        <iframe
                            src="https://maps.google.com/maps?q=<?php echo MAP_LAT; ?>,<?php echo MAP_LNG; ?>&z=15&output=embed"
                            width="100%"
                            height="450"
                            frameborder="0"
                            style="border:0;"
                            allowfullscreen=""
                            aria-hidden="true"
                            tabindex="999">
                        </iframe>
                    </div>
                </div>
            </div>

            <div class="row mt-3 border-top pt-3">
                <div class="col-12 text-center">
                    <p class="m-0"><?php echo $lang['footer_copyright']; ?></p>
                </div>
            </div>
        </div>
    </footer>
    <!-- jQuery must be loaded first -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Slick Carousel -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    
    <!-- Lightbox -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    
    <!-- AOS Animation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
        // Initialize AOS after DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            AOS.init({
                duration: 800,
                once: true
            });
        });
    </script>
    
    <!-- Main JS -->
    <script src="assets/js/main.js"></script>
    <?php
    if ($currentLang == 'ar'):
        $rtlOrltr = 'true';
    else:
        $rtlOrltr = 'false';
    endif; ?>
    <script>
        $(document).ready(function() {
            // Initialize Slick Carousel for KIA cars
            $('.kia-carousel').slick({
                dots: false,
                infinite: true,
                speed: 600,
                slidesToShow: 1,
                slidesToScroll: 1,
                centerMode: true,
                centerPadding: '25%',
                arrows: false,
                autoplay: false,

                rtl: <?php echo $rtlOrltr; ?>,
                responsive: [{
                        breakpoint: 1200,
                        settings: {
                            centerPadding: '20%',
                        },
                    },
                    {
                        breakpoint: 768,
                        settings: {
                            centerPadding: '15%',
                        },
                    },
                    {
                        breakpoint: 480,
                        settings: {
                            centerPadding: '10%',
                        },
                    },
                ],
            });

            // Initialize Reviews Carousel
            $('.reviews-carousel').slick({
                dots: true,
                infinite: true,
                speed: 800,
                slidesToShow: 3,
                slidesToScroll: 1,
                autoplay: true,
                autoplaySpeed: 5000,
                pauseOnHover: true,
                arrows: true,
                rtl: <?php echo $rtlOrltr; ?>,
                prevArrow: '<button type="button" class="slick-prev"><i class="fas fa-chevron-left"></i></button>',
                nextArrow: '<button type="button" class="slick-next"><i class="fas fa-chevron-right"></i></button>',
                responsive: [
                    {
                        breakpoint: 992,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 1,
                        }
                    },
                    {
                        breakpoint: 768,
                        settings: {
                            slidesToShow: 1,
                            slidesToScroll: 1,
                            arrows: false,
                        }
                    }
                ]
            });

            // Counter animation for statistics
            function animateCounters() {
                $('.stat-number').each(function() {
                    const $this = $(this);
                    const countTo = $this.text().replace(/[^\d]/g, ''); // Extract numbers only
                    
                    if (countTo) {
                        $({ countNum: 0 }).animate({
                            countNum: countTo
                        }, {
                            duration: 2000,
                            easing: 'swing',
                            step: function() {
                                const num = Math.floor(this.countNum);
                                if ($this.text().includes('%')) {
                                    $this.text(num + '%');
                                } else if ($this.text().includes('+')) {
                                    $this.text(num + '+');
                                } else if ($this.text().includes('.')) {
                                    $this.text((this.countNum).toFixed(1));
                                } else {
                                    $this.text(num);
                                }
                            },
                            complete: function() {
                                $this.text($this.text()); // Ensure final value is set
                            }
                        });
                    }
                });
            }

            // Trigger counter animation when section comes into view
            if (typeof AOS !== 'undefined') {
                $(document).on('aos:in', '.customer-reviews-section', function() {
                    animateCounters();
                });
            } else {
                // Fallback for when AOS is not available
                $(window).on('scroll', function() {
                    const reviewsSection = $('.customer-reviews-section');
                    if (reviewsSection.length) {
                        const sectionTop = reviewsSection.offset().top;
                        const sectionHeight = reviewsSection.outerHeight();
                        const windowTop = $(window).scrollTop();
                        const windowHeight = $(window).height();
                        
                        if (windowTop + windowHeight > sectionTop + 100) {
                            animateCounters();
                            $(window).off('scroll'); // Run only once
                        }
                    }
                });
            }
        });
    </script>
    <script src="assets/js/slick-option.js"></script>

    </body>

    </html>

    <?php
    ob_end_flush();
    ?>