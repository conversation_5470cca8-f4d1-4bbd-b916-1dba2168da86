    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Common JavaScript -->
    <script>
        $(document).ready(function() {
            // تفعيل DataTables
            if ($('.data-table').length) {
                $('.data-table').DataTable({
                    language: {
                        url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                    },
                    responsive: true,
                    pageLength: 10,
                    order: [
                        [0, 'desc']
                    ]
                });
            }

            // تبديل عرض الشريط الجانبي
            $('#toggleSidebar').on('click', function() {
                $('#sidebar').toggleClass('collapsed');
                $('.main-content').toggleClass('expanded');
            });

            // تبديل عرض الشريط الجانبي في الجوال
            $('#toggleSidebarMobile').on('click', function() {
                $('#sidebar').toggleClass('show');
            });

            // إخفاء الشريط الجانبي عند النقر خارجه في الجوال
            $(document).on('click', function(e) {
                if ($(window).width() <= 768) {
                    if (!$(e.target).closest('#sidebar, #toggleSidebarMobile').length) {
                        $('#sidebar').removeClass('show');
                    }
                }
            });

            // تفعيل tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // تأكيد الحذف (عدم تطبيق ذلك على أزرار حذف صور السيارات)
            $('.btn-delete').not('.btn-delete[data-image-id]').on('click', function(e) {
                e.preventDefault();
                var url = $(this).attr('href');
                var itemName = $(this).data('name') || 'هذا العنصر';

                Swal.fire({
                    title: 'تأكيد الحذف',
                    text: 'هل أنت متأكد من حذف ' + itemName + '؟',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#e74c3c',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = url;
                    }
                });
            });

            // رسائل النجاح والخطأ
            <?php if (isset($_SESSION['success_message'])): ?>
                Swal.fire({
                    title: 'تم بنجاح!',
                    text: '<?php echo $_SESSION['success_message']; ?>',
                    icon: 'success',
                    confirmButtonText: 'موافق'
                });
                <?php unset($_SESSION['success_message']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error_message'])): ?>
                Swal.fire({
                    title: 'خطأ!',
                    text: '<?php echo $_SESSION['error_message']; ?>',
                    icon: 'error',
                    confirmButtonText: 'موافق'
                });
                <?php unset($_SESSION['error_message']); ?>
            <?php endif; ?>
        });

        // دالة عامة لإرسال طلبات AJAX
        function sendAjaxRequest(url, data, successCallback, errorCallback) {
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                dataType: 'json',
                beforeSend: function() {
                    // إظهار مؤشر التحميل
                    Swal.fire({
                        title: 'جاري المعالجة...',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                },
                success: function(response) {
                    Swal.close();
                    if (response.success) {
                        if (successCallback) {
                            successCallback(response);
                        } else {
                            Swal.fire({
                                title: 'تم بنجاح!',
                                text: response.message,
                                icon: 'success'
                            }).then(() => {
                                location.reload();
                            });
                        }
                    } else {
                        Swal.fire({
                            title: 'خطأ!',
                            text: response.message,
                            icon: 'error'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.close();
                    if (errorCallback) {
                        errorCallback(xhr, status, error);
                    } else {
                        Swal.fire({
                            title: 'خطأ في الاتصال!',
                            text: 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.',
                            icon: 'error'
                        });
                    }
                }
            });
        }

        // دالة لتنسيق الأرقام
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // دالة لتنسيق التاريخ
        function formatDate(dateString) {
            var date = new Date(dateString);
            var options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            return date.toLocaleDateString('ar-SA', options);
        }

        // دالة للتحقق من صحة البريد الإلكتروني
        function validateEmail(email) {
            var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        // دالة للتحقق من صحة رقم الهاتف
        function validatePhone(phone) {
            var re = /^[0-9+\-\s()]+$/;
            return re.test(phone) && phone.length >= 10;
        }

        // دالة لمعاينة الصور قبل الرفع
        function previewImage(input, previewElement) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $(previewElement).attr('src', e.target.result).show();
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // تفعيل معاينة الصور
        $('.image-input').on('change', function() {
            var previewId = $(this).data('preview');
            if (previewId) {
                previewImage(this, '#' + previewId);
            }
        });

        // دالة لإضافة حقول ديناميكية
        function addDynamicField(containerId, fieldHtml) {
            $('#' + containerId).append(fieldHtml);
        }

        // دالة لحذف حقل ديناميكي
        $(document).on('click', '.remove-field', function() {
            $(this).closest('.dynamic-field').remove();
        });

        // حفظ حالة الشريط الجانبي
        if (localStorage.getItem('sidebarCollapsed') === 'true') {
            $('#sidebar').addClass('collapsed');
        }

        $('#toggleSidebar').on('click', function() {
            var isCollapsed = $('#sidebar').hasClass('collapsed');
            localStorage.setItem('sidebarCollapsed', !isCollapsed);
        });
    </script>

    <!-- Custom Page Scripts -->
    <?php if (isset($customScript)): ?>
        <script>
            <?php echo $customScript; ?>
        </script>
    <?php endif; ?>
    </body>

    </html>