<?php

/**
 * الصفحة الرئيسية للوحة التحكم
 * Dashboard Main Page
 */

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// عنوان الصفحة
$pageTitle = 'لوحة التحكم الرئيسية';

try {
    // إحصائيات السيارات
    $totalCars = $db->Fetch("SELECT COUNT(*) as count FROM cars")['count'];
    $soldCars = $db->Fetch("SELECT COUNT(*) as count FROM cars WHERE is_sold = 1")['count'];
    $availableCars = $totalCars - $soldCars;

    // إحصائيات الفئات
    $totalCategories = $db->Fetch("SELECT COUNT(*) as count FROM car_categories")['count'];

    // إحصائيات المستخدمين (للمدراء فقط)
    $totalUsers = 0;
    if ($_SESSION['is_admin'] == 1) {
        $totalUsers = $db->Fetch("SELECT COUNT(*) as count FROM users")['count'];
    }

    // أحدث السيارات المضافة
    $recentCars = $db->FetchAll("
        SELECT c.*, cat.category_ar, 
               (SELECT image_url FROM car_images WHERE car_id = c.car_id AND is_main = 1 LIMIT 1) as main_image
        FROM cars c 
        LEFT JOIN car_categories cat ON c.category_id = cat.category_id 
        ORDER BY c.created_at DESC 
        LIMIT 5
    ");
} catch (Exception $e) {
    $error = "خطأ في استرجاع البيانات: " . $e->getMessage();
}

// تضمين ملف الرأس
include 'includes/header.php';
?>

<!-- محتوى الصفحة الرئيسية -->
<div class="container-fluid p-4">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">لوحة التحكم الرئيسية</h1>
        <div class="text-muted">
            <i class="fas fa-calendar-alt me-1"></i>
            <?php echo formatDateArabic(date('Y-m-d h:i:s a')); ?>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <!-- إجمالي السيارات -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-primary me-3">
                            <i class="fas fa-car"></i>
                        </div>
                        <div>
                            <div class="stat-number"><?php echo number_format($totalCars); ?></div>
                            <div class="stat-label">إجمالي السيارات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- السيارات المتاحة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-success me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <div class="stat-number"><?php echo number_format($availableCars); ?></div>
                            <div class="stat-label">السيارات المتاحة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- السيارات المباعة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-warning me-3">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div>
                            <div class="stat-number"><?php echo number_format($soldCars); ?></div>
                            <div class="stat-label">السيارات المباعة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجمالي الفئات -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-info me-3">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div>
                            <div class="stat-number"><?php echo number_format($totalCategories); ?></div>
                            <div class="stat-label">فئات السيارات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- صف الإحصائيات الإضافية للمدراء -->
    <?php if ($_SESSION['is_admin'] == 1): ?>
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-secondary me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <div class="stat-number"><?php echo number_format($totalUsers); ?></div>
                                <div class="stat-label">المستخدمين</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-danger me-3">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <div class="stat-number"><?php echo $soldCars > 0 ? number_format(($soldCars / $totalCars) * 100, 1) : 0; ?>%</div>
                                <div class="stat-label">معدل المبيعات</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- أحدث السيارات المضافة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        أحدث السيارات المضافة
                    </h5>
                    <a href="pages/cars/index.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentCars)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-car text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">لا توجد سيارات مضافة بعد</p>
                            <a href="pages/cars/add.php" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة سيارة جديدة
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>الماركة والموديل</th>
                                        <th>السنة</th>
                                        <th>السعر</th>
                                        <th>الفئة</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentCars as $car): ?>
                                        <tr>
                                            <td>
                                                <?php if ($car['main_image']): ?>
                                                    <img src="<?php echo UPLOAD_PATH . $car['main_image']; ?>"
                                                        alt="<?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar']); ?>"
                                                        class="img-thumbnail"
                                                        style="width: 60px; height: 45px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                                        style="width: 60px; height: 45px;">
                                                        <i class="fas fa-car text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($car['brand_ar']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($car['model_ar']); ?></small>
                                            </td>
                                            <td><?php echo $car['year']; ?></td>
                                            <td>
                                                <strong><?php echo number_format($car['price']); ?></strong>
                                                <small class="text-muted"><?php echo $car['currency']; ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo htmlspecialchars($car['category_ar'] ?? 'غير محدد'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($car['is_sold']): ?>
                                                    <span class="badge bg-warning">مباع</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">متاح</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small><?php echo formatDateArabic($car['created_at']); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="pages/cars/view.php?id=<?php echo $car['car_id']; ?>"
                                                        class="btn btn-outline-info"
                                                        data-bs-toggle="tooltip"
                                                        title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="pages/cars/edit.php?id=<?php echo $car['car_id']; ?>"
                                                        class="btn btn-outline-primary"
                                                        data-bs-toggle="tooltip"
                                                        title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        روابط سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="pages/cars/add.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span>إضافة سيارة جديدة</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3 add-category-btn">
                                <i class="fas fa-tag fa-2x mb-2"></i>
                                <span>إضافة فئة جديدة</span>
                            </a>
                        </div>
                        <?php if ($_SESSION['is_admin'] == 1): ?>
                            <div class="col-md-3 mb-3">
                                <a href="#" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3 add-user-btn">
                                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                                    <span>إضافة مستخدم جديد</span>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="pages/settings/index.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                    <i class="fas fa-cog fa-2x mb-2"></i>
                                    <span>إعدادات الموقع</span>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // التعامل مع نقرات أزرار الروابط السريعة
    $(document).ready(function() {
        // إضافة فئة جديدة
        $('.add-category-btn').on('click', function(e) {
            e.preventDefault();
            window.location.href = 'pages/categories/index.php';
        });

        // إضافة مستخدم جديد
        $('.add-user-btn').on('click', function(e) {
            e.preventDefault();
            window.location.href = 'pages/users/index.php';
        });
    });
</script>

<?php
// تضمين ملف التذييل
include 'includes/footer.php';
?>