@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
:root {
  --font-rtl: 'Cairo', sans-serif;
  --font-ltr: 'Poppins', sans-serif;

  --primary-color: #05141f;
  --secondary-color: #000000;
  --accent-color: #05141f;

  --bg-light: #ffffff;
  --bg-dark: #05141f;
  --bg-gradient: linear-gradient(135deg, #05141f, #000000);
  --bg-gradient-alt: linear-gradient(135deg, #000000, #05141f);

  --text-dark: #000000;
  --text-medium: #05141f;
  --text-light: #ffffff;

  --success-color: #05141f;
  --info-color: #05141f;
  --warning-color: #000000;
  --danger-color: #000000;

  --card-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
  --header-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --border-radius: 0px;
}

html[dir='rtl'] body {
  font-family: var(--font-rtl) !important;
}

html[dir='ltr'] body {
  font-family: var(--font-ltr) !important;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

body {
  color: var(--text-dark);
  line-height: 1.8;
  overflow-x: hidden;
  background-color: var(--bg-light);
  transition: background-color 0.3s ease;
}
.tshadow {
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5) !important;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  position: relative;
}

h2 {
  font-size: 2.2rem;
  position: relative;
}

h3 {
  font-size: 1.8rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
}

a:hover {
  color: var(--secondary-color);
}
.text-primary {
  color: rgb(45 65 68) !important;
}
.btn {
  border-radius: var(--border-radius);
  padding: 12px 24px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  overflow: hidden;
  position: relative;
  z-index: 1;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: rgba(0, 0, 0, 0.1);
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: -1;
}

.btn:hover::after {
  height: 100%;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.07);
}

.btn-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.07);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  animation: floatParticle linear infinite;
}

@keyframes floatParticle {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-1000px) translateX(200px);
    opacity: 0;
  }
}
.bg-primary {
  background-color: rgb(0 0 0) !important;
}
.shadow-custom {
  box-shadow: var(--card-shadow);
}

.section-title {
  position: relative;
  padding-bottom: 20px;
}

.section-title::after {
  content: '';
  position: absolute;
  display: block;
  width: 50px;
  height: 3px;
  background: var(--primary-color);
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

body[dir='rtl'] .section-title::after {
  left: auto;
  right: 0;
}

.navbar {
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  background-color: rgba(255, 255, 255, 0.98) !important;
  box-shadow: var(--header-shadow);
  backdrop-filter: blur(10px);
}

.navbar-brand {
  position: relative;
  padding: 0;
}

.navbar-brand img {
  max-height: 50px;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link {
  font-weight: 600;
  padding: 20px 15px !important;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  text-align: center;
}

.navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 3px;
  bottom: 10px;
  left: 50%;
  background-color: var(--primary-color);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: translateX(-50%);
  opacity: 0;
  border-radius: 10px;
}

.navbar-nav .nav-link.active::after,
.navbar-nav .nav-link:hover::after {
  width: 30px;
  opacity: 1;
}

.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link:hover {
  color: var(--primary-color);
  transform: translateY(-3px);
}

.navbar-toggler {
  border: none;
  padding: 10px;
}

.navbar-toggler:focus {
  box-shadow: none;
  outline: none;
}

.navbar-toggler-icon {
  width: 24px;
  height: 24px;
  transition: all 0.3s ease;
}

.lang-switch {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0;
  padding: 8px 18px;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.lang-switch:hover {
  background: var(--text-light);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.hero-section {
  position: relative;
  padding-top: 180px;
  padding-bottom: 100px;
  background-color: var(--bg-light);
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #05141f 100% 100%);
}

.hero-section::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-image: url('../images/pattern.svg');
  background-size: cover;
  opacity: 0.05;
  z-index: 0;
}

.animated-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0.5;
  z-index: 1;
  overflow: hidden;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.animated-bg::before {
  content: '';
  position: absolute;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  background: var(--bg-gradient-alt);
  top: -200px;
  right: -100px;
  animation: float 15s infinite ease-in-out;
  opacity: 0.6;
}

.animated-bg::after {
  content: '';
  position: absolute;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  background: var(--bg-gradient);
  bottom: -150px;
  left: -100px;
  animation: float 12s infinite ease-in-out 1s reverse;
  opacity: 0.6;
}

.shape-1,
.shape-2,
.shape-3 {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  filter: blur(30px);
}

.shape-1 {
  width: 200px;
  height: 200px;
  background-color: rgba(76, 175, 80, 0.15);
  top: 20%;
  left: 15%;
  animation: float 20s infinite ease-in-out 2s;
}

.shape-2 {
  width: 300px;
  height: 300px;
  background-color: rgba(33, 150, 243, 0.15);
  bottom: 10%;
  right: 10%;
  animation: float 25s infinite ease-in-out;
}

.shape-3 {
  width: 150px;
  height: 150px;
  background-color: rgba(229, 24, 55, 0.15);
  top: 30%;
  right: 30%;
  animation: float 18s infinite ease-in-out 1s;
}

@keyframes float {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  33% {
    transform: translate(30px, -30px) rotate(10deg) scale(1.05);
  }
  66% {
    transform: translate(-20px, 20px) rotate(-7deg) scale(0.95);
  }
  100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
}

.hero-section .container {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: var(--bg-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--text-medium);
}

.hero-btn {
  padding: 14px 32px;
  font-size: 1rem;
  letter-spacing: 1px;
  text-transform: uppercase;
  border-radius: 50px;
  box-shadow: 0 10px 30px rgba(229, 24, 55, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hero-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(229, 24, 55, 0.4);
}

.hero-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.6s;
  z-index: -1;
}

.hero-btn:hover::before {
  left: 100%;
}

.hero-image {
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.hero-image img {
  transform: rotateY(-5deg) rotateX(5deg);
  transition: all 0.5s ease;
  box-shadow: 30px 30px 30px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.hero-image:hover img {
  transform: rotateY(0) rotateX(0);
}

.min-vh-80 {
  min-height: 80vh;
}

.services-section {
  position: relative;
  padding: 100px 0;
  background-color: var(--bg-light);
  z-index: 1;
}

.services-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../images/pattern.svg');
  opacity: 0.04;
  z-index: -1;
}

.section-title {
  position: relative;
  margin-bottom: 50px;
  text-align: center;
}

.section-title h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.section-title h2::after {
  content: '';
  position: absolute;
  width: 80px;
  height: 4px;
  border-radius: 10px;
  background: var(--bg-gradient);
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.section-subtitle {
  color: var(--text-medium);
  font-size: 1.1rem;
  max-width: 700px;
  margin: 0 auto;
}

.service-card {
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
  height: 100%;
  border: none;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background: var(--bg-gradient);
  opacity: 0.03;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: -1;
}

.service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.service-card:hover::before {
  height: 100%;
}

.service-card:hover .service-icon {
  transform: rotateY(360deg);
  background: var(--bg-gradient);
}

.service-card:hover .service-icon i {
  color: white;
}

.service-icon {
  width: 90px;
  height: 90px;
  margin: 0 auto 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #05141f;
  transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  margin-top: -45px;
  border: 5px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.service-icon i {
  font-size: 35px;
  color: var(--primary-color);
  transition: all 0.8s ease;
}

.service-card .card-body {
  padding: 30px 20px;
  text-align: center;
}

.service-card .card-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.service-card .card-text {
  color: var(--text-medium);
  margin-bottom: 25px;
}

.service-btn {
  color: var(--primary-color);
  font-weight: 600;
  position: relative;
  transition: all 0.3s ease;
}

.service-btn::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color);
  transition: all 0.3s ease;
}

.service-btn:hover::after {
  width: 100%;
}

.service-btn i {
  transition: all 0.3s ease;
}

.service-btn:hover i {
  transform: translateX(5px);
}

.cars-section {
  position: relative;
  padding: 100px 0;
  background-color: var(--bg-light);
}

.cars-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../images/pattern.svg');
  opacity: 0.04;
  z-index: 0;
}

.filter-buttons {
  margin-bottom: 40px;
}

.filter-btn {
  padding: 10px 25px;
  margin: 0 5px 10px;
  border-radius: 0;
  font-weight: 600;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 8px 20px rgba(229, 24, 55, 0.3);
  border-radius: 0;
}

.filter-btn:not(.active):hover {
  background-color: rgba(229, 24, 55, 0.1);
  transform: translateY(-3px);
}

.car-card {
  border: none;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  margin-bottom: 30px;
  background-color: white;
  position: relative;
  z-index: 1;
  height: 100%;
}

.car-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--bg-gradient);
  transform: scaleX(0);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 2;
  opacity: 0;
}

.car-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12) !important;
}

.car-card:hover::after {
  transform: scaleX(1);
  opacity: 1;
}

.car-img {
  height: 220px;
  overflow: hidden;
  position: relative;
}

.car-img img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: all 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.car-card:hover .car-img img {
  transform: scale(1.1) rotate(2deg);
}

.car-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.2) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.car-card:hover .car-overlay {
  opacity: 1;
}

.car-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 7px 15px;
  border-radius: 20px;
  background: var(--accent-color);
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  z-index: 2;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.car-content {
  padding: 25px;
  position: relative;
}

.car-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--text-dark);
  transition: all 0.3s ease;
}

.car-card:hover .car-title {
  color: var(--primary-color);
}

.car-description {
  color: var(--text-medium);
  margin-bottom: 20px;
  font-size: 0.95rem;
  line-height: 1.7;
}

.car-info {
  display: flex;
  justify-content: space-between;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.car-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--accent-color);
}

.car-year {
  font-size: 0.9rem;
  color: var(--text-medium);
  display: flex;
  align-items: center;
}

.car-year i {
  color: var(--primary-color);
  margin-right: 5px;
}

.view-details-btn {
  padding: 10px 20px;
  border-radius: 30px;
  background-color: white;
  color: var(--primary-color);
  font-weight: 600;
  border: 2px solid var(--primary-color);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.view-details-btn:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.contact-section {
  position: relative;
  padding: 100px 0;
  background-color: var(--bg-light);
}

.contact-form {
  border-radius: 0;
  box-shadow: var(--card-shadow);
  background-color: white;
  height: 100%;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.contact-form::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(229, 24, 55, 0.03),
    rgba(0, 0, 0, 0.03)
  );
  transform: rotate(45deg);
  transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: -1;
}

.contact-form:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.contact-form:hover::before {
  top: -20%;
  left: -20%;
}

.contact-info {
  border-radius: var(--border-radius);
  height: 100%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.07);
  border-radius: 0;
}

.contact-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-gradient);
  opacity: 0.9;
  z-index: -1;
}

.contact-info::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../images/pattern.svg');
  background-size: cover;
  opacity: 0.05;
  z-index: -1;
}

.form-floating {
  margin-bottom: 20px;
}

.form-control {
  border-radius: var(--border-radius);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 15px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(229, 24, 55, 0.1);
}

.contact-icon-box {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.contact-icon-box i {
  font-size: 22px;
  color: var(--primary-color);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.contact-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.contact-info-item:hover .contact-icon-box {
  transform: scale(1.1) rotate(10deg);
}

.contact-info-item:hover .contact-icon-box i {
  transform: scale(1.2);
}

.contact-info-content h5 {
  color: white;
  margin-bottom: 5px;
  font-weight: 600;
}

.contact-info-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
}

.social-icons {
  margin-top: 20px;
}

.social-icon {
  width: 50px;
  height: 50px;
  background-color: white;
  color: var(--primary-color);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.social-icon:hover {
  transform: translateY(-10px) rotate(10deg);
  color: white;
  background-color: var(--secondary-color);
}

.map-container {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  height: 450px;
  margin-top: 50px;
}

.map-container iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

footer {
  background-color: var(--bg-dark);
  color: white;
  position: relative;
  overflow: hidden;
}

/* footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../images/pattern.svg');
  background-size: cover;
  opacity: 0.05;
  z-index: 0;
} */

.footer-top {
  padding: 80px 0 50px;
  position: relative;
  z-index: 1;
}

.footer-bottom {
  padding: 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  position: relative;
  z-index: 1;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-logo img {
  max-height: 60px;
  margin-bottom: 20px;
}

.footer-about p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 25px;
}

.footer-heading {
  font-size: 1.5rem;
  margin-bottom: 25px;
  color: white;
  font-weight: 700;
  position: relative;
  padding-bottom: 10px;
}

.footer-heading::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 3px;
  background: var(--secondary-color);
  border-radius: 10px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.footer-links a:hover {
  color: var(--secondary-color);
  transform: translateX(8px);
}

.footer-links a i {
  margin-right: 10px;
  font-size: 12px;
  color: var(--secondary-color);
}

.footer-contact {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.footer-contact-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: var(--secondary-color);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.footer-contact:hover .footer-contact-icon {
  transform: scale(1.2);
  background-color: var(--secondary-color);
  color: white;
}

.footer-contact-text h6 {
  color: white;
  margin-bottom: 5px;
}

.footer-contact-text p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0;
}

.social-icons a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  margin-right: 10px;
  color: white;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-icons a:hover {
  transform: translateY(-10px) rotate(360deg);
  background-color: var(--secondary-color);
  color: white !important;
}

.copyright {
  color: rgba(255, 255, 255, 0.6);
}

.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: var(--secondary-color);
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.home-hero__gradient {
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  width: 100%;
  z-index: 0;
}
@media (max-width: 992px) {
  .navbar-nav .nav-link {
    padding: 10px 15px !important;
  }

  .hero-section {
    padding-top: 150px;
    padding-bottom: 80px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    text-align: center;
    padding-top: 120px;
    padding-bottom: 60px;
  }

  .section-title::after {
    left: 50%;
  }

  body[dir='rtl'] .section-title::after {
    right: 50%;
    margin-right: -25px;
    margin-left: 0;
  }
}

.fade-in {
  animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

body[dir='rtl'] .ms-auto {
  margin-right: auto !important;
  margin-left: 0 !important;
}

body[dir='rtl'] .ms-3 {
  margin-right: 1rem !important;
  margin-left: 0 !important;
}

body[dir='rtl'] .me-3 {
  margin-left: 1rem !important;
  margin-right: 0 !important;
}

body[dir='rtl'] .ms-2 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

body[dir='rtl'] .me-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

.alert-box {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 5px;
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1050;
  min-width: 300px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

body[dir='rtl'] .alert-box {
  right: auto;
  left: 20px;
  animation: slideInLeft 0.5s ease-in-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.btn {
  border-radius: 4px;
  padding: 8px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #004494;
  border-color: #004494;
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}
#about img {
  border-radius: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out;
}

.preloader-content {
  text-align: center;
}

.preloader-logo {
  max-width: 200px;
  margin-bottom: 30px;
  animation: pulse 2s infinite, fadeIn 1.5s ease-out;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #05141f;
  border-radius: 50%;
  margin: 0 auto;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.text-rotator {
  position: relative;
  height: 80px;
  overflow: hidden;
}

.rotating-text {
  position: absolute;
  width: 100%;
  color: #05141f;
  font-weight: bold;
  font-size: 16px;
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
  filter: blur(2px);
  transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1), filter 0.6s ease-out;
}

.rotating-text.active {
  opacity: 1;
  transform: translateY(0);
  filter: blur(0);
}
.single-car {
  background-color: #f8f9fa;
  padding: 30px 0;
}
.single-car .car-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
.single-car .gallery-thumb {
  cursor: pointer;
  transition: transform 0.3s;
  margin-bottom: 15px;
  border: 2px solid #ddd;
  border-radius: 5px;
}
.single-car .gallery-thumb:hover {
  transform: scale(1.03);
  border-color: #0d6efd;
}
.single-car .specs-list {
  list-style-type: none;
  padding: 0;
}
.single-car .specs-list li {
  padding: 10px 0;
  border-bottom: 1px dashed #dee2e6;
}
.single-car .specs-list li:last-child {
  border-bottom: none;
}
.single-car .spec-icon {
  width: 30px;
  text-align: center;
  margin-left: 10px;
  color: rgb(45 65 68);
}
.single-car .price-badge {
  font-size: 1.5rem;
  padding: 10px 20px;
  border-radius: 50px;
}

/* Customer Reviews Section - Professional & Formal Design */
.customer-reviews-section {
  position: relative;
  background: #ffffff;
  padding: 80px 0;
}

.reviews-carousel {
  position: relative;
  z-index: 1;
}

.review-card {
  padding: 0 15px;
  margin-bottom: 30px;
}

.review-card .card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #ffffff;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
  position: relative;
}

.review-card .card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Remove Quote Icon */
.review-quote-icon {
  display: none;
}

/* Avatar Styles - Simplified */
.review-avatar {
  position: relative;
}

.avatar-icon {
  width: 60px;
  height: 60px;
  background: #f8f9fa;
  border: 2px solid #dee2e6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
}

.avatar-icon i {
  font-size: 1.5rem;
}

/* Verified Badge - Simplified */
.verified-badge {
  bottom: -2px;
  right: -2px;
  background: #28a745;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #ffffff;
}

.verified-badge i {
  font-size: 10px;
  color: #ffffff;
}

/* Stars - Professional */
.stars i {
  font-size: 14px;
  margin-right: 1px;
  color: #ffc107;
}

/* Blockquote - Clean */
.blockquote {
  position: relative;
  font-style: normal;
  color: #495057;
  line-height: 1.6;
  font-size: 0.9rem;
  margin: 0;
  font-weight: 400;
}

/* Meta Icons - Minimal */
.meta-icon {
  width: 18px;
  height: 18px;
  background: #f8f9fa;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.meta-icon i {
  font-size: 10px;
  color: #6c757d;
}

/* Statistics - Professional */
.stat-item {
  padding: 30px 20px;
  text-align: center;
  border-radius: 8px;
  background: #ffffff;
  border: 1px solid #e9ecef;
  transition: box-shadow 0.3s ease;
}

.stat-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  margin-bottom: 15px;
}

.stat-number {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 10px;
}

.stat-label {
  font-weight: 500;
  font-size: 0.85rem;
  color: #6c757d;
  text-transform: none;
  letter-spacing: normal;
}

/* CTA Section - Clean */
.cta-wrapper {
  position: relative;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #ffffff;
  transition: box-shadow 0.3s ease;
}

.cta-wrapper:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.cta-bg-decoration {
  display: none;
}

.cta-icon {
  width: 50px;
  height: 50px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e9ecef;
}

.cta-icon i {
  font-size: 1.5rem;
  color: #495057;
}

/* Professional Card Body */
.review-card .card-body {
  padding: 25px;
}

/* Customer Name */
.review-card h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

/* Review Text */
.review-text {
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

/* Review Meta */
.review-meta {
  border-top: 1px solid #f8f9fa;
  padding-top: 15px;
  margin-top: 15px;
}

.review-meta small {
  font-size: 0.8rem;
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .review-card {
    padding: 0 10px;
  }
  
  .customer-reviews-section {
    padding: 60px 0;
  }
  
  .avatar-icon {
    width: 50px;
    height: 50px;
  }
  
  .avatar-icon i {
    font-size: 1.2rem;
  }
  
  .stat-item {
    margin-bottom: 20px;
    padding: 25px 15px;
  }
}

@media (max-width: 576px) {
  .review-card .card-body {
    padding: 20px;
  }
  
  .stat-number {
    font-size: 2rem !important;
  }
}

/* Remove all animations and complex effects */
.review-card .card,
.stat-item,
.cta-wrapper,
.avatar-icon {
  animation: none !important;
  transform: none !important;
}

/* Clean hover states only */
.review-card .card:hover,
.stat-item:hover,
.cta-wrapper:hover {
  transform: none !important;
}

/* Read More Button - Professional & Simple */
.read-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  text-decoration: none !important;
  color: #495057 !important;
  background: #f8f9fa;
  border: 1px solid #dee2e6 !important;
  border-radius: 4px;
  padding: 6px 12px !important;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.read-more-btn:hover {
  color: #2c3e50 !important;
  background: #e9ecef;
  border-color: #adb5bd !important;
}

.read-more-btn:active {
  background: #dee2e6;
}


/* Modal Styles */
.modal-content {
  border-radius: 20px;
  overflow: hidden;
}

.modal-header {
  border-radius: 20px 20px 0 0;
  padding: 25px 30px;
}

.modal-body {
  padding: 30px;
}

.modal-footer {
  border-radius: 0 0 20px 20px;
  padding: 20px 30px;
}

.modal-avatar .avatar-icon {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.modal-stars i {
  font-size: 14px;
  margin-right: 2px;
}

/* Review Cards Equal Height */
.review-card .card {
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.review-card .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.review-card .blockquote {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.review-card .review-meta {
  margin-top: auto;
}

/* Responsive Modal */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 10px;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }
  
  .modal-avatar .avatar-icon {
    width: 40px;
    height: 40px;
  }
  
  .modal-avatar .avatar-icon i {
    font-size: 1.2rem;
  }
}
