<?php

/**
 * ملف الدوال المشتركة
 * Common Functions File
 */

/**
 * تنظيف البيانات المدخلة
 * Sanitize input data
 */
function sanitizeInput($data)
{
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * التحقق من صحة البريد الإلكتروني
 * Validate email format
 */
function validateEmail($email)
{
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * تشفير كلمة المرور
 * Hash password
 */
function hashPassword($password)
{
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 * Verify password
 */
function verifyPassword($password, $hash)
{
    return password_verify($password, $hash);
}

/**
 * رفع الملفات بأمان
 * Secure file upload
 */
function uploadFile($file, $targetDir, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'avif', 'mp4', 'webm'])
{
    $uploadOk = 1;
    $imageFileType = strtolower(pathinfo($file["name"], PATHINFO_EXTENSION));
    $fileName = uniqid() . '.' . $imageFileType;
    $targetFile = $targetDir . $fileName;

    // التحقق من أن الملف صورة حقيقية
    if (isset($file["tmp_name"])) {
        // التحقق من امتداد الملف أولاً
        if (!in_array($imageFileType, $allowedTypes)) {
            return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
        }

        // للصيغ المدعومة بواسطة getimagesize (jpg, png, gif)
        if (in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
            $check = getimagesize($file["tmp_name"]);
            if ($check === false) {
                return ['success' => false, 'message' => 'الملف ليس صورة صحيحة'];
            }
        }
        // للفيديو (mp4)
        elseif ($imageFileType === 'mp4') {
            // التحقق من بداية ملف MP4
            $handle = fopen($file["tmp_name"], 'rb');
            $header = fread($handle, 8);
            fclose($handle);
            if (substr($header, 4, 4) !== 'ftyp') {
                return ['success' => false, 'message' => 'ملف MP4 غير صالح'];
            }
        }
        // للصيغ الأخرى (webp, avif, svg) نتحقق من المحتوى
        elseif ($imageFileType === 'webp') {
            // التحقق من بداية ملف WEBP
            $handle = fopen($file["tmp_name"], 'rb');
            $header = fread($handle, 12);
            fclose($handle);
            if (substr($header, 0, 4) !== 'RIFF' || substr($header, 8, 4) !== 'WEBP') {
                return ['success' => false, 'message' => 'ملف WEBP غير صالح'];
            }
        } elseif ($imageFileType === 'avif') {
            // التحقق من بداية ملف AVIF
            $handle = fopen($file["tmp_name"], 'rb');
            $header = fread($handle, 12);
            fclose($handle);
            if (substr($header, 4, 8) !== 'ftypavif' && substr($header, 4, 8) !== 'ftavisof') {
                return ['success' => false, 'message' => 'ملف AVIF غير صالح'];
            }
        } elseif ($imageFileType === 'svg') {
            // التحقق من محتوى SVG
            $content = file_get_contents($file["tmp_name"]);
            if (strpos($content, '<svg') === false) {
                return ['success' => false, 'message' => 'ملف SVG غير صالح'];
            }
        }
    }

    // التحقق من حجم الملف (50MB للفيديو، 5MB للصور)
    $maxSize = (in_array($imageFileType, ['mp4'])) ? 50000000 : 5000000;
    if ($file["size"] > $maxSize) {
        $fileTypeText = (in_array($imageFileType, ['mp4'])) ? 'ملف فيديو' : 'ملف صورة';
        return ['success' => false, 'message' => 'حجم الملف كبير جداً (الحد الأقصى لل' . $fileTypeText . ' هو 50MB)'];
    }

    // تم نقل فحص نوع الملف إلى قسم فحص الصور أعلاه

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!file_exists($targetDir)) {
        mkdir($targetDir, 0777, true);
    }

    // رفع الملف
    if (move_uploaded_file($file["tmp_name"], $targetFile)) {
        return ['success' => true, 'filename' => $fileName, 'path' => $targetFile];
    } else {
        return ['success' => false, 'message' => 'حدث خطأ أثناء رفع الملف'];
    }
}

/**
 * حذف الملف
 * Delete file
 */
function deleteFile($filePath)
{
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return true;
}

/**
 * تنسيق التاريخ
 * Format date
 */
function formatDate($date, $format = 'Y-m-d H:i:s')
{
    return date($format, strtotime($date));
}

/**
 * تحويل التاريخ للعربية
 * Convert date to Arabic
 */
function formatDateArabic($date)
{
    $months = [
        1 => 'يناير',
        2 => 'فبراير',
        3 => 'مارس',
        4 => 'أبريل',
        5 => 'مايو',
        6 => 'يونيو',
        7 => 'يوليو',
        8 => 'أغسطس',
        9 => 'سبتمبر',
        10 => 'أكتوبر',
        11 => 'نوفمبر',
        12 => 'ديسمبر'
    ];

    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);

    // الحصول على الوقت وتحويل AM/PM إلى عربي
    $time = date('h:i', $timestamp);
    $ampm = date('A', $timestamp);

    // تحويل AM/PM إلى عربي
    $arabicAmPm = ($ampm == 'AM') ? 'صباحاً' : 'مساءً';

    return "$day $month $year - $time $arabicAmPm";
}

/**
 * تقسيم النتائج للصفحات
 * Pagination
 */
function paginate($totalRecords, $recordsPerPage = 10, $currentPage = 1)
{
    $totalPages = ceil($totalRecords / $recordsPerPage);
    $offset = ($currentPage - 1) * $recordsPerPage;

    return [
        'total_records' => $totalRecords,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'records_per_page' => $recordsPerPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages
    ];
}

/**
 * إنشاء أزرار التنقل بين الصفحات
 * Generate pagination buttons
 */
function generatePaginationHTML($pagination, $baseUrl)
{
    if ($pagination['total_pages'] <= 1) {
        return '';
    }

    $html = '<nav aria-label="التنقل بين الصفحات">';
    $html .= '<ul class="pagination justify-content-center">';

    // زر الصفحة السابقة
    if ($pagination['has_previous']) {
        $prevPage = $pagination['current_page'] - 1;
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . $baseUrl . '?page=' . $prevPage . '">السابق</a>';
        $html .= '</li>';
    }

    // أرقام الصفحات
    $start = max(1, $pagination['current_page'] - 2);
    $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

    for ($i = $start; $i <= $end; $i++) {
        $active = ($i == $pagination['current_page']) ? 'active' : '';
        $html .= '<li class="page-item ' . $active . '">';
        $html .= '<a class="page-link" href="' . $baseUrl . '?page=' . $i . '">' . $i . '</a>';
        $html .= '</li>';
    }

    // زر الصفحة التالية
    if ($pagination['has_next']) {
        $nextPage = $pagination['current_page'] + 1;
        $html .= '<li class="page-item">';
        $html .= '<a class="page-link" href="' . $baseUrl . '?page=' . $nextPage . '">التالي</a>';
        $html .= '</li>';
    }

    $html .= '</ul>';
    $html .= '</nav>';

    return $html;
}

/**
 * إرسال استجابة JSON
 * Send JSON response
 */
function jsonResponse($success, $message, $data = null)
{
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

/**
 * التحقق من وجود البيانات المطلوبة
 * Check required fields
 */
function validateRequired($data, $requiredFields)
{
    $missing = [];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * تصغير الصورة
 * Resize image
 */
function resizeImage($sourcePath, $targetPath, $maxWidth, $maxHeight)
{
    $imageInfo = getimagesize($sourcePath);
    if (!$imageInfo) {
        return false;
    }

    $sourceWidth = $imageInfo[0];
    $sourceHeight = $imageInfo[1];
    $sourceType = $imageInfo[2];

    // حساب الأبعاد الجديدة
    $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
    $newWidth = intval($sourceWidth * $ratio);
    $newHeight = intval($sourceHeight * $ratio);

    // إنشاء الصورة حسب النوع
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($sourcePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($sourcePath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }

    // إنشاء صورة جديدة
    $newImage = imagecreatetruecolor($newWidth, $newHeight);

    // الحفاظ على الشفافية للـ PNG
    if ($sourceType == IMAGETYPE_PNG) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
    }

    // تصغير الصورة
    imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);

    // حفظ الصورة
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            imagejpeg($newImage, $targetPath, 90);
            break;
        case IMAGETYPE_PNG:
            imagepng($newImage, $targetPath);
            break;
        case IMAGETYPE_GIF:
            imagegif($newImage, $targetPath);
            break;
    }

    // تنظيف الذاكرة
    imagedestroy($sourceImage);
    imagedestroy($newImage);

    return true;
}
