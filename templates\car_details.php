<?php
$currentLang = getCurrentLanguage();
$name = ($currentLang == 'ar') ? htmlspecialchars($car['brand_ar']) . ' ' . htmlspecialchars($car['model_ar']) : htmlspecialchars($car['brand_en']) . ' ' . htmlspecialchars($car['model_en']);
$currentUrl = getCurrentUrl();

// Get main car image for background
$mainCarImage = '';
if (!empty($mainImages)) {
    $mainCarImage = UPLOADS_PATH . htmlspecialchars($mainImages[0]['image_url']);
} else {
    $mainCarImage = DEFAULT_IMAGE;
}
?>

<!-- Enhanced Hero Section -->
<section class="car-hero position-relative" style="background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('<?php echo $mainCarImage; ?>'); background-size: cover; background-position: center; background-attachment: fixed; min-height: 100vh; padding-top: 120px;">
    <div class="container h-100">
        <div class="row h-100 align-items-center">
            <div class="col-lg-8">
                <div class="hero-content text-white" data-aos="fade-right">
                    <div class="d-flex align-items-center mb-3">
                        <?php if ($rating['total'] > 0): ?>
                            <div class="rating-stars me-3">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?php echo $i <= $rating['average'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                <?php endfor; ?>
                                <span class="ms-2"><?php echo $rating['average']; ?> (<?php echo $rating['total']; ?> <?php echo $lang['reviews_count']; ?>)</span>
                            </div>
                        <?php endif; ?>
                        <span class="badge bg-success px-3 py-2"><?php echo $car['is_sold'] ? $lang['not_available'] : $lang['available']; ?></span>
                    </div>
                    <h1 class="display-4 fw-bold mb-3"><?php echo $name; ?></h1>
                    <p class="lead mb-4"><?php echo ($currentLang == 'ar') ? $car['category_ar'] : $car['category_en']; ?> • <?php echo $car['year']; ?></p>
                    <div class="price-display mb-4">
                        <span class="h2 text-warning fw-bold">$<?php echo number_format($car['price'] ?? 0); ?></span>
                        <span class="text-light ms-2"><?php echo $car['currency'] ?? 'USD'; ?></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-actions text-center" data-aos="fade-left">
                    <div class="action-buttons d-grid gap-2">
                        <a href="<?php echo createLink('contact.php'); ?>" class="btn btn-warning btn-lg">
                            <i class="fas fa-phone me-2"></i><?php echo $lang['contact_now']; ?>
                        </a>
                        <button class="btn btn-outline-light btn-lg" onclick="toggleComparison(<?php echo $car['car_id']; ?>)" id="comparison-btn-<?php echo $car['car_id']; ?>">
                            <i class="fas fa-balance-scale me-2"></i><?php echo $lang['add_to_comparison']; ?>
                        </button>
                        <button class="btn btn-outline-light btn-lg" onclick="toggleShare()">
                            <i class="fas fa-share-alt me-2"></i><?php echo $lang['share_car']; ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Comparison Counter -->
    <?php if ($comparisonCount > 0): ?>
        <div class="comparison-float">
            <a href="<?php echo createLink('compare.php'); ?>" class="btn btn-primary rounded-pill">
                <i class="fas fa-balance-scale me-2"></i>
                <?php echo $lang['compare_cars']; ?> (<?php echo $comparisonCount; ?>)
            </a>
        </div>
    <?php endif; ?>
</section>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-share-alt me-2"></i><?php echo $lang['share_car']; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-6">
                        <button class="btn btn-primary w-100" onclick="shareOnFacebook()">
                            <i class="fab fa-facebook-f me-2"></i><?php echo $lang['share_on_facebook']; ?>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-info w-100" onclick="shareOnTwitter()">
                            <i class="fab fa-twitter me-2"></i><?php echo $lang['share_on_twitter']; ?>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-success w-100" onclick="shareOnWhatsApp()">
                            <i class="fab fa-whatsapp me-2"></i><?php echo $lang['share_on_whatsapp']; ?>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-secondary w-100" onclick="copyLink()">
                            <i class="fas fa-copy me-2"></i><?php echo $lang['copy_link']; ?>
                        </button>
                    </div>
                </div>
                <div class="mt-3">
                    <input type="text" class="form-control" id="shareUrl" value="<?php echo $currentUrl; ?>" readonly>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$name = ($currentLang == 'ar') ? htmlspecialchars($car['brand_ar']) . ' ' . htmlspecialchars($car['model_ar']) : htmlspecialchars($car['brand_en']) . ' ' . htmlspecialchars($car['model_en']);
?>
<section class="single-car py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo createLink('index.php'); ?>"><i class="fas fa-home"></i> <?php echo $lang['menu_home']; ?></a></li>
                        <li class="breadcrumb-item"><a href="<?php echo createLink('cars.php'); ?>"><?php echo $lang['menu_cars']; ?></a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo $name; ?></li>
                    </ol>
                </nav>
                <h1 class="fw-bold text-primary"><?php echo $name; ?></h1>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-7 mb-4">
                <div class="car-header">

                    <div class="main-image text-center mb-4">
                        <?php foreach ($mainImages as $image): ?>
                            <a href="<?= !empty($image['image_url']) ? UPLOADS_PATH . htmlspecialchars($image['image_url']) : DEFAULT_IMAGE ?>" data-lightbox="car-gallery" data-title="<?php echo $name; ?>">
                                <img src="<?= !empty($image['image_url']) ? UPLOADS_PATH . htmlspecialchars($image['image_url']) : DEFAULT_IMAGE ?>" alt="<?php echo $name; ?>" class="img-fluid rounded-3">
                            </a>
                        <?php endforeach; ?>
                    </div>
                    <div class="row g-2">
                        <?php foreach ($galleryImages as $image): ?>
                            <div class="col-3">
                                <a href="<?= !empty($image['image_url']) ? UPLOADS_PATH . htmlspecialchars($image['image_url']) : DEFAULT_IMAGE ?>" data-lightbox="car-gallery" data-title="<?php echo $name; ?>">
                                    <img src="<?= !empty($image['image_url']) ? UPLOADS_PATH . htmlspecialchars($image['image_url']) : DEFAULT_IMAGE ?>" alt="<?php echo $name; ?>" class="img-fluid gallery-thumb rounded">
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i><?php echo $lang['car_specifications']; ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="specs-list">
                                        <li>
                                            <i class="fas fa-car-alt spec-icon"></i>
                                            <?php echo $lang['brand']; ?>:
                                            <?php echo ($currentLang == 'ar') ? $car['brand_ar'] : $car['brand_en']; ?>
                                        </li>
                                        <li>
                                            <i class="fas fa-gas-pump spec-icon"></i>
                                            <?php echo $lang['car_fuel_type']; ?>:
                                            <?php echo ($currentLang == 'ar') ? $car['fuel_type_ar'] : $car['fuel_type_en']; ?>
                                        </li>
                                        <li>
                                            <i class="fas fa-cog spec-icon"></i>
                                            <?php echo $lang['transmission_type']; ?>:
                                            <?php echo ($currentLang == 'ar') ? $car['transmission_ar'] : $car['transmission_en']; ?>
                                        </li>
                                        <li>
                                            <i class="fas fa-car spec-icon"></i>
                                            <?php echo $lang['doors']; ?>:
                                            <?php echo $car['doors'] ?? '--'; ?>
                                        </li>

                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="specs-list">
                                        <li>
                                            <i class="fas fa-tag spec-icon"></i>
                                            <?php echo $lang['model']; ?>:
                                            <?php echo ($currentLang == 'ar') ? $car['model_ar'] : $car['model_en']; ?>
                                        </li>
                                        <li>
                                            <i class="fas fa-oil-can spec-icon"></i>
                                            <?php echo $lang['engine_size']; ?>:
                                            <?php echo $car['engine_size'] ?? '--'; ?>
                                        </li>
                                        <li>
                                            <i class="fas fa-road spec-icon"></i>
                                            <?php echo $lang['mileage']; ?>:
                                            <?php echo number_format($car['mileage'] ?? 0); ?>
                                        </li>
                                        <li>
                                            <i class="fas fa-users spec-icon"></i>
                                            <?php echo $lang['seats']; ?>:
                                            <?php echo $car['seats'] ?? '--'; ?>
                                        </li>
                                    </ul>
                                </div>
                                <?php if (!empty($video['video_url'])) { ?>
                                    <div class="col-md-12">

                                        <video class="w-full h-full object-cover object-center" style="  width: 100%;
  height: auto;
  display: block;" controls autoplay loop muted poster="<?php echo DEFAULT_IMAGE ?>">
                                            <source src="<?php echo UPLOADS_PATH . htmlspecialchars($video['video_url']); ?>" type="video/mp4">
                                        </video>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-5">
                <div class="sticky-top" style="top: 20px;">
                    <div class="card mb-4 shadow-sm">
                        <div class="card-body text-center">
                            <h4 class="text-muted mb-3"><?php echo $lang['start_price']; ?></h4>
                            <div class="price-badge bg-primary text-white d-inline-block mb-3">
                                <?php echo ($car['price'] ?? '0') . ' ' . $car['currency']; ?>
                            </div>
                            <!--<p class="text-success"><i class="fas fa-tag me-2"></i>خصم 5% للدفع النقدي</p>-->
                            <a href="<?php echo createLink('contact.php'); ?>" class="btn btn-danger btn-lg w-100 mb-3">
                                <i class="fas fa-phone-alt me-2"></i><?php echo $lang['contact_now']; ?>
                            </a>
                            <!--<button class="btn btn-outline-primary btn-lg w-100">
                                <i class="fas fa-envelope me-2"></i>طلب عرض سعر
                            </button>-->
                        </div>
                    </div>
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-star me-2"></i> <?= $lang['car_features'] ?></h5>
                        </div>
                        <div class="card-body">
                            <?php
                            foreach ($organizedFeatures as $type => $features) { ?>
                                <div class="d-flex align-items-start mb-3">
                                    <div class="flex-shrink-0 text-primary">
                                        <i class="fas fa-check-circle fa-lg"></i>
                                    </div>
                                    <div class="ms-3">
                                        <h5 class="mb-1"><?= htmlspecialchars($type) ?></h5>
                                        <?php foreach ($features as $feature): ?>
                                            <li>
                                                <?= ($currentLang == 'ar') ? htmlspecialchars($feature['feature_name_ar']) : htmlspecialchars($feature['feature_name_en']); ?>:
                                                <?php
                                                if ($currentLang == 'ar') {
                                                    if (!empty($feature['feature_value_ar'])) {
                                                        echo '<strong>' . $feature['feature_value_ar'] . '</strong>';
                                                    } else {
                                                        echo '<strong>متاح</strong>';
                                                    }
                                                } else {
                                                    if (!empty($feature['feature_value_en'])) {
                                                        echo '<strong>' . $feature['feature_value_en'] . '</strong>';
                                                    } else {
                                                        echo '<strong>Available</strong>';
                                                    }
                                                }
                                                ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php }

                            ?>
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0 text-primary">
                                    <i class="fas fa-check-circle fa-lg"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="mb-1"><?= $lang['car_description'] ?></h5>
                                    <p class="mb-0 text-muted" style="text-align: justify;"><?= ($currentLang == 'ar') ? $car['description_ar'] : $car['description_en']; ?></p>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i><?= $lang['sale_information'] ?></h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <th><i class="fas fa-calendar-alt text-primary me-2"></i> <?= $lang['model'] ?></th>
                                    <td><?php echo $car['year'] ?? '--'; ?></td>
                                </tr>
                                <tr>
                                    <th><i class="fas fa-palette text-primary me-2"></i> <?= $lang['color'] ?></th>
                                    <td><?= ($currentLang == 'ar') ? $car['color_ar'] : $car['color_en']; ?></td>
                                </tr>
                                <tr>
                                    <th><i class="fas fa-home text-primary me-2"></i> <?= $lang['is_sold'] ?></th>
                                    <td><?= $car['is_sold'] ? $lang['not_available'] : $lang['available']; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Reviews Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="mb-0">
                                <i class="fas fa-comments me-2"></i><?php echo $lang['customer_reviews']; ?>
                            </h3>
                            <?php if ($rating['total'] > 0): ?>
                                <div class="rating-summary text-end">
                                    <div class="rating-stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= $rating['average'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <small><?php echo $rating['average']; ?> <?php echo $lang['based_on_reviews']; ?> <?php echo $rating['total']; ?> <?php echo $lang['reviews_count']; ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($reviews)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted"><?php echo $lang['no_reviews']; ?></h5>
                                <button class="btn btn-primary mt-3" onclick="toggleReviewForm()">
                                    <i class="fas fa-plus me-2"></i><?php echo $lang['write_review']; ?>
                                </button>
                            </div>
                        <?php else: ?>
                            <!-- Display Reviews -->
                            <div class="reviews-list mb-4">
                                <?php 
                                $approvedReviews = array_filter($reviews, fn($review) => $review['is_approved'] == 1);
                                $pendingReviews = array_filter($reviews, fn($review) => $review['is_approved'] == 0);
                                ?>
                                
                                <?php foreach ($approvedReviews as $review): ?>
                                    <div class="review-item border-bottom pb-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($review['reviewer_name']); ?></h6>
                                                <div class="rating-stars">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-warning' : 'text-muted'; ?> fa-sm"></i>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                            <small class="text-muted"><?php echo date('d/m/Y', strtotime($review['created_at'])); ?></small>
                                        </div>
                                        <?php if (!empty($review['review_title'])): ?>
                                            <h6 class="text-primary"><?php echo htmlspecialchars($review['review_title']); ?></h6>
                                        <?php endif; ?>
                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['review_content'])); ?></p>
                                    </div>
                                <?php endforeach; ?>
                                
                                <?php if (!empty($pendingReviews)): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-clock me-2"></i>
                                        <?php echo ($currentLang == 'ar') ? 
                                            count($pendingReviews) . ' تقييم في انتظار الموافقة الإدارية' : 
                                            count($pendingReviews) . ' review(s) pending admin approval'; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="text-center">
                                <button class="btn btn-outline-primary" onclick="toggleReviewForm()">
                                    <i class="fas fa-plus me-2"></i><?php echo $lang['write_review']; ?>
                                </button>
                            </div>
                        <?php endif; ?>

                        <!-- Review Form -->
                        <div id="reviewForm" class="review-form mt-4" style="display: none;">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-edit me-2"></i><?php echo $lang['write_review']; ?>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form action="process_review.php" method="POST">
                                        <input type="hidden" name="car_id" value="<?php echo $car['car_id']; ?>">

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label"><?php echo $lang['your_name']; ?> *</label>
                                                <input type="text" class="form-control" name="reviewer_name" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label"><?php echo $lang['your_email']; ?> *</label>
                                                <input type="email" class="form-control" name="reviewer_email" required>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label"><?php echo $lang['rating']; ?> *</label>
                                            <div class="rating-input">
                                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                                    <input type="radio" name="rating" value="<?php echo $i; ?>" id="star<?php echo $i; ?>" required>
                                                    <label for="star<?php echo $i; ?>"><i class="fas fa-star"></i></label>
                                                <?php endfor; ?>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label"><?php echo $lang['review_title']; ?></label>
                                            <input type="text" class="form-control" name="review_title" placeholder="<?php echo ($currentLang == 'ar') ? 'عنوان اختياري للتقييم' : 'Optional review title'; ?>">
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label"><?php echo $lang['your_review']; ?> *</label>
                                            <textarea class="form-control" name="review_content" rows="4" required placeholder="<?php echo ($currentLang == 'ar') ? 'شاركنا تجربتك مع هذه السيارة...' : 'Share your experience with this car...'; ?>"></textarea>
                                        </div>

                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-paper-plane me-2"></i><?php echo $lang['submit_review']; ?>
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="toggleReviewForm()">
                                                <i class="fas fa-times me-2"></i><?php echo ($currentLang == 'ar') ? 'إلغاء' : 'Cancel'; ?>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Similar Cars Section -->
        <?php if (!empty($similarCars)): ?>
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h3 class="mb-0">
                                <i class="fas fa-car me-2"></i><?php echo $lang['similar_cars']; ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <?php foreach ($similarCars as $similarCar):
                                    $similarName = ($currentLang == 'ar') ?
                                        htmlspecialchars($similarCar['brand_ar']) . ' ' . htmlspecialchars($similarCar['model_ar']) :
                                        htmlspecialchars($similarCar['brand_en']) . ' ' . htmlspecialchars($similarCar['model_en']);
                                ?>
                                    <div class="col-lg-3 col-md-6">
                                        <div class="card h-100 similar-car-card">
                                            <div class="position-relative">
                                                <img src="<?php echo UPLOADS_PATH . ($similarCar['main_image'] ?? 'default.jpg'); ?>"
                                                    alt="<?php echo $similarName; ?>"
                                                    class="card-img-top" style="height: 200px; object-fit: cover;">
                                                <div class="card-overlay">
                                                    <a href="<?php echo createLink('car_details.php', true, $similarCar['car_id']); ?>"
                                                        class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i><?php echo $lang['view_details']; ?>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <h6 class="card-title"><?php echo $similarName; ?></h6>
                                                <p class="card-text text-muted small">
                                                    <?php echo ($currentLang == 'ar') ? $similarCar['category_ar'] : $similarCar['category_en']; ?> •
                                                    <?php echo $similarCar['year']; ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="text-primary fw-bold">$<?php echo number_format($similarCar['price'] ?? 0); ?></span>
                                                    <button class="btn btn-outline-primary btn-sm" onclick="toggleComparison(<?php echo $similarCar['car_id']; ?>)">
                                                        <i class="fas fa-balance-scale"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-4">
                                <a href="<?php echo createLink('cars.php'); ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-car me-2"></i><?php echo $lang['view_all_cars']; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
<!-- Enhanced CSS Styles -->
<style>
    .comparison-float {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        animation: pulse 2s infinite;
    }

    .rating-input {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .rating-input input[type="radio"] {
        display: none;
    }

    .rating-input label {
        cursor: pointer;
        color: #ddd;
        font-size: 1.5rem;
        margin: 0 2px;
        transition: color 0.2s;
    }

    .rating-input input[type="radio"]:checked~label,
    .rating-input label:hover,
    .rating-input label:hover~label {
        color: #ffc107;
    }

    .similar-car-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .similar-car-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .similar-car-card:hover .card-overlay {
        opacity: 1;
    }

    .price-badge {
        padding: 15px 25px;
        border-radius: 50px;
        font-size: 1.5rem;
        font-weight: bold;
    }

    .specs-list {
        list-style: none;
        padding: 0;
    }

    .specs-list li {
        padding: 8px 0;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
    }

    .spec-icon {
        width: 20px;
        margin-right: 10px;
        color: var(--primary-color);
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }

        100% {
            transform: scale(1);
        }
    }

    .review-item:last-child {
        border-bottom: none !important;
    }

    .rating-stars i {
        margin-right: 2px;
    }

    .hero-actions .btn {
        border-radius: 50px;
        padding: 12px 24px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .car-hero {
        position: relative;
        overflow: hidden;
    }

    .car-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.1;
    }
</style>

<script>
    // Enhanced JavaScript functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AOS animations
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 1000,
                once: true
            });
        }

        // Show success/error messages
        <?php if (isset($_SESSION['review_success'])): ?>
            showNotification('<?php echo $_SESSION['review_success']; ?>', 'success');
            <?php unset($_SESSION['review_success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['review_error'])): ?>
            showNotification('<?php echo $_SESSION['review_error']; ?>', 'error');
            <?php unset($_SESSION['review_error']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['comparison_success'])): ?>
            showNotification('<?php echo $_SESSION['comparison_success']; ?>', 'success');
            <?php unset($_SESSION['comparison_success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['comparison_error'])): ?>
            showNotification('<?php echo $_SESSION['comparison_error']; ?>', 'error');
            <?php unset($_SESSION['comparison_error']); ?>
        <?php endif; ?>
    });

    // Toggle review form
    function toggleReviewForm() {
        const form = document.getElementById('reviewForm');
        if (form.style.display === 'none' || form.style.display === '') {
            form.style.display = 'block';
            form.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        } else {
            form.style.display = 'none';
        }
    }

    // Toggle share modal
    function toggleShare() {
        const modal = new bootstrap.Modal(document.getElementById('shareModal'));
        modal.show();
    }

    // Social sharing functions
    function shareOnFacebook() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent('<?php echo $name; ?>');
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${title}`, '_blank', 'width=600,height=400');
    }

    function shareOnTwitter() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent('<?php echo $name; ?> - <?php echo ($currentLang == 'ar') ? 'ديار الكرم للسيارات' : 'Diyar Alkaram Motors'; ?>');
        window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
    }

    function shareOnWhatsApp() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent('<?php echo $name; ?> - <?php echo ($currentLang == 'ar') ? 'ديار الكرم للسيارات' : 'Diyar Alkaram Motors'; ?>');
        window.open(`https://wa.me/?text=${title} ${url}`, '_blank');
    }

    function copyLink() {
        const urlInput = document.getElementById('shareUrl');
        urlInput.select();
        urlInput.setSelectionRange(0, 99999);

        try {
            document.execCommand('copy');
            showNotification('<?php echo $lang['link_copied']; ?>', 'success');
        } catch (err) {
            // Fallback for modern browsers
            navigator.clipboard.writeText(urlInput.value).then(() => {
                showNotification('<?php echo $lang['link_copied']; ?>', 'success');
            });
        }
    }

    // Comparison functionality
    function toggleComparison(carId) {
        const btn = document.getElementById(`comparison-btn-${carId}`);
        const isAdding = btn.textContent.includes('<?php echo $lang['add_to_comparison']; ?>');

        fetch('process_comparison.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `action=${isAdding ? 'add' : 'remove'}&car_id=${carId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update button text and style
                    if (isAdding) {
                        btn.innerHTML = '<i class="fas fa-balance-scale me-2"></i><?php echo $lang['remove_from_comparison']; ?>';
                        btn.classList.remove('btn-outline-light');
                        btn.classList.add('btn-warning');
                    } else {
                        btn.innerHTML = '<i class="fas fa-balance-scale me-2"></i><?php echo $lang['add_to_comparison']; ?>';
                        btn.classList.remove('btn-warning');
                        btn.classList.add('btn-outline-light');
                    }

                    // Update comparison counter
                    updateComparisonCounter(data.count);

                    // Show notification
                    showNotification(data.message, 'success');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('<?php echo ($currentLang == 'ar') ? 'حدث خطأ، يرجى المحاولة مرة أخرى' : 'An error occurred, please try again'; ?>', 'error');
            });
    }

    // Update comparison counter
    function updateComparisonCounter(count) {
        const floatingCounter = document.querySelector('.comparison-float');
        if (count > 0) {
            if (!floatingCounter) {
                // Create floating counter if it doesn't exist
                const counter = document.createElement('div');
                counter.className = 'comparison-float';
                counter.innerHTML = `
                <a href="<?php echo createLink('compare.php'); ?>" class="btn btn-primary rounded-pill">
                    <i class="fas fa-balance-scale me-2"></i>
                    <?php echo $lang['compare_cars']; ?> (${count})
                </a>
            `;
                document.body.appendChild(counter);
            } else {
                // Update existing counter
                floatingCounter.querySelector('a').innerHTML = `
                <i class="fas fa-balance-scale me-2"></i>
                <?php echo $lang['compare_cars']; ?> (${count})
            `;
            }
        } else if (floatingCounter) {
            floatingCounter.remove();
        }
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.custom-notification');
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = `custom-notification alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Legacy function for backward compatibility
    function applyFilter(e, categoryValue) {
        e.preventDefault();
        const url = new URL(window.location.href);

        if (categoryValue === 'all') {
            url.searchParams.delete('category');
        } else {
            url.searchParams.set('category', categoryValue);
        }

        window.location.href = url.toString();
        return false;
    }
</script>