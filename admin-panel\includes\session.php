<?php
/**
 * ملف إدارة الجلسات والتحقق من صلاحيات الدخول
 * Session Management and Authentication Check
 */

// تضمين ملف الإعدادات إذا لم يكن مضمناً
if (!defined('DB_HOST')) {
    require_once 'config.php';
}

/**
 * التحقق من تسجيل دخول المستخدم
 * Check if user is logged in
 */
function checkLogin() {
    // التحقق من وجود جلسة نشطة
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        redirectToLogin();
        exit();
    }

    // التحقق من انتهاء صلاحية الجلسة
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        destroySession();
        redirectToLogin();
        exit();
    }

    // تحديث وقت آخر نشاط
    $_SESSION['last_activity'] = time();
}

/**
 * التحقق من صلاحيات الإدارة
 * Check admin privileges
 */
function checkAdminPrivileges() {
    checkLogin();

    if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
        die('<div class="alert alert-danger text-center">ليس لديك صلاحيات للوصول لهذه الصفحة</div>');
    }
}

/**
 * توجيه المستخدم لصفحة تسجيل الدخول
 * Redirect to login page
 */
function redirectToLogin() {
    $loginUrl = BASE_URL . 'auth/login.php';
    header("Location: $loginUrl");
    exit();
}

/**
 * إنهاء الجلسة وحذف جميع البيانات
 * Destroy session and clear all data
 */
function destroySession() {
    session_unset();
    session_destroy();

    // حذف ملفات تعريف الارتباط إن وجدت
    if (isset($_COOKIE[session_name()])) {
        setcookie(session_name(), '', time() - 3600, '/');
    }
}

/**
 * الحصول على بيانات المستخدم الحالي
 * Get current user data
 */
function getCurrentUser() {
    global $db;

    if (!isset($_SESSION['user_id'])) {
        return null;
    }

    try {
        $stmt = "SELECT id, fullname, username, is_admin FROM users WHERE id = :user_id";
        $user = $db->Fetch($stmt, ['user_id' => $_SESSION['user_id']]);
        return $user;
    } catch (Exception $e) {
        return null;
    }
}

/**
 * تسجيل عملية تسجيل الدخول
 * Log login attempt
 */
function logLoginAttempt($username, $success = false, $ip = null) {
    global $db;

    if ($ip === null) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    try {
        $stmt = "INSERT INTO login_logs (username, ip_address, success, created_at) VALUES (:username, :ip, :success, NOW())";
        $db->Insert($stmt, [
            'username' => $username,
            'ip' => $ip,
            'success' => $success ? 1 : 0
        ]);
    } catch (Exception $e) {
        // في حالة عدم وجود جدول login_logs، يمكن تجاهل الخطأ
    }
}

/**
 * إنشاء رمز CSRF للحماية من الهجمات
 * Generate CSRF token for security
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * التحقق من رمز CSRF
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * إنشاء حقل HTML لرمز CSRF
 * Generate CSRF token HTML input field
 */
function getCSRFTokenField() {
    $token = generateCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
}
?>