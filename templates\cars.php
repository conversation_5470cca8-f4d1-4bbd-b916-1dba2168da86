<?php
$currentLang = getCurrentLanguage();

$category = isset($_GET['category']) ? $_GET['category'] : 'all';
$brand = isset($_GET['brand']) ? $_GET['brand'] : 'all';

$cars = filterCars($category, $brand);

?>

<!-- Cars Hero Section -->
<section class="page-hero position-relative text-center" style="background-color: #000000;">

    <div role="group" aria-roledescription="slide" class="min-w-0 shrink-0 grow-0 basis-full relative flex-shrink-0 pl-0">
        <video class="w-full h-full object-cover object-center" style="  width: 100%;
  height: auto;
  display: block;opacity: 0.3;" autoplay="" loop="" playsinline="" preload="none" poster="<?php echo ASSETS_PATH ?>images/6_3_USP_Beyond_the_essentials_2_183a18d74d.png">
            <source src="<?php echo ASSETS_PATH ?>videos/6_3_USP_Beyond_the_essentials_2_1_1f875ade32.mp4" type="video/mp4">
            <source src="<?php echo ASSETS_PATH ?>images/6_3_USP_Beyond_the_essentials_2_183a18d74d.png" type="image/png">
            <source src="<?php echo DEFAULT_IMAGE ?>" type="image/png">Your browser does not support the video tag.
        </video>
        <div class="text-white flex flex-col gap-5 pb-5 w-full position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
            <h1 class="display-4 fw-bold mb-3 text-white animate__animated animate__fadeInDown" style="text-transform: uppercase; letter-spacing: 1.5px;"><?php echo $lang['cars_title']; ?></h1>
            <div class="divider mx-auto mb-2" style="height: 4px; width: 70px; background-color: #05141f;"></div>
            <div class="text-rotator" style="width: 600px;">
                <p class="lead mb-4 text-white rotating-text active" style="font-size: 20px!important;"><?php echo $lang['cars_subtitle1']; ?></p>
                <p class="lead mb-4 text-white rotating-text" style="font-size: 20px!important;"><?php echo $lang['cars_subtitle2']; ?></p>
                <p class="lead mb-4 text-white rotating-text" style="font-size: 20px!important;"><?php echo $lang['cars_subtitle3']; ?></p>
                <p class="lead mb-4 text-white rotating-text" style="font-size: 20px!important;"><?php echo $lang['cars_subtitle4']; ?></p>
                <p class="lead mb-4 text-white rotating-text" style="font-size: 20px!important;"><?php echo $lang['cars_subtitle5']; ?></p>
                <p class="lead mb-4 text-white rotating-text" style="font-size: 20px!important;"><?php echo $lang['cars_subtitle6']; ?></p>
            </div>
            <div class="d-flex justify-content-center gap-3 animate__animated animate__fadeInUp" data-aos="fade-up" data-aos-delay="200">
                <a href="<?php echo createLink('contact.php'); ?>" class="btn btn-lg" style="background-color: #000000; color: white; padding: 12px 30px; border-radius: 0; font-weight: 500;">
                    <i class="fas fa-envelope me-2"></i><?php echo $lang['contact_us']; ?>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="py-4 bg-white">
    <div class="container">
        <div class="row">
            <!-- Brand Filter -->
            <div class="col-12 mb-4">
                <h5 class="mb-3 text-center"><?php echo ($currentLang == 'ar') ? 'تصفية حسب العلامة التجارية' : 'Filter by Brand'; ?></h5>
                <div class="brand-filter text-center" data-aos="fade-up">
                    <a href="<?php 
                        $params = [];
                        if ($category !== 'all') $params['category'] = $category;
                        echo createLink('cars.php', false, null, null, $params); 
                    ?>"
                        class="btn <?php echo ($brand === 'all') ? 'btn-primary' : 'btn-outline-primary'; ?> m-1"
                        onclick="return applyBrandFilter(event, 'all')">
                        <i class="fas fa-th-large me-2"></i><?php echo ($currentLang == 'ar') ? 'جميع العلامات' : 'All Brands'; ?>
                    </a>
                    <?php
                    $brands = getAllBrands();
                    foreach ($brands as $brandItem):
                        $brandName = $brandItem['brand_name'];
                        $isActive = ($brand === $brandName);
                        $params = [];
                        if ($category !== 'all') $params['category'] = $category;
                        $params['brand'] = $brandName;
                    ?>
                        <a href="<?php echo createLink('cars.php', false, null, null, $params); ?>"
                            class="btn <?php echo $isActive ? 'btn-primary' : 'btn-outline-primary'; ?> m-1"
                            onclick="return applyBrandFilter(event, '<?php echo htmlspecialchars($brandName); ?>')">
                            <i class="fas fa-car me-2"></i><?php echo htmlspecialchars($brandName); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Category Filter -->
            <div class="col-12">
                <h5 class="mb-3 text-center"><?php echo ($currentLang == 'ar') ? 'تصفية حسب الفئة' : 'Filter by Category'; ?></h5>
                <div class="category-filter text-center" data-aos="fade-up" data-aos-delay="100">
                    <a href="<?php 
                        $params = [];
                        if ($brand !== 'all') $params['brand'] = $brand;
                        echo createLink('cars.php', false, null, null, $params); 
                    ?>"
                        class="btn <?php echo ($category === 'all') ? 'btn-primary' : 'btn-outline-primary'; ?> m-1"
                        onclick="return applyCategoryFilter(event, 'all')">
                        <i class="fas fa-list me-2"></i><?php echo $lang['filter_all']; ?>
                    </a>
                    <?php
                    $categories = getAllCategories();
                    foreach ($categories as $cat):
                        $isActive = ($category == $cat['category_id']);
                        $params = [];
                        if ($brand !== 'all') $params['brand'] = $brand;
                        $params['category'] = $cat['category_id'];
                    ?>
                        <a href="<?php echo createLink('cars.php', false, null, null, $params); ?>"
                            class="btn <?php echo $isActive ? 'btn-primary' : 'btn-outline-primary'; ?> m-1"
                            onclick="return applyCategoryFilter(event, <?= $cat['category_id'] ?>)">
                            <?php if (!empty($cat['icon'])): ?>
                                <?php echo $cat['icon']; ?>
                            <?php else: ?>
                                <i class="fas fa-tag me-2"></i>
                            <?php endif; ?>
                            <?php echo htmlspecialchars($cat['display_name']); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Active Filters Display -->
            <?php if ($category !== 'all' || $brand !== 'all'): ?>
            <div class="col-12 mt-3">
                <div class="active-filters text-center" data-aos="fade-up" data-aos-delay="200">
                    <h6 class="mb-2"><?php echo ($currentLang == 'ar') ? 'المرشحات النشطة:' : 'Active Filters:'; ?></h6>
                    <?php if ($brand !== 'all'): ?>
                        <span class="badge bg-primary me-2 mb-2">
                            <?php echo ($currentLang == 'ar') ? 'العلامة التجارية: ' : 'Brand: '; ?><?php echo htmlspecialchars($brand); ?>
                            <a href="<?php 
                                $params = [];
                                if ($category !== 'all') $params['category'] = $category;
                                echo createLink('cars.php', false, null, null, $params); 
                            ?>" class="text-white ms-1">×</a>
                        </span>
                    <?php endif; ?>
                    <?php if ($category !== 'all'): ?>
                        <?php
                        $categoryName = '';
                        foreach ($categories as $cat) {
                            if ($cat['category_id'] == $category) {
                                $categoryName = $cat['display_name'];
                                break;
                            }
                        }
                        ?>
                        <span class="badge bg-secondary me-2 mb-2">
                            <?php echo ($currentLang == 'ar') ? 'الفئة: ' : 'Category: '; ?><?php echo htmlspecialchars($categoryName); ?>
                            <a href="<?php 
                                $params = [];
                                if ($brand !== 'all') $params['brand'] = $brand;
                                echo createLink('cars.php', false, null, null, $params); 
                            ?>" class="text-white ms-1">×</a>
                        </span>
                    <?php endif; ?>
                    <a href="<?php echo createLink('cars.php'); ?>" class="btn btn-sm btn-outline-danger ms-2">
                        <i class="fas fa-times me-1"></i><?php echo ($currentLang == 'ar') ? 'مسح جميع المرشحات' : 'Clear All Filters'; ?>
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Cars Gallery Section -->
<section class="py-5 bg-light">
    <div class="container">
        <?php if (empty($cars)): ?>
            <div class="col-12 text-center">
                <div class="alert alert-info" role="alert">
                    <?php echo ($currentLang == 'ar') ? 'لا توجد سيارات في هذه الفئة حالياً.' : 'No cars in this category at the moment.'; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="row g-4">
                <?php foreach ($cars as $index => $car):
                    $delay = ($index % 3 + 1) * 100;
                    $name = ($currentLang == 'ar') ? htmlspecialchars($car['brand_ar']) . ' ' . htmlspecialchars($car['model_ar']) : htmlspecialchars($car['brand_en']) . ' ' . htmlspecialchars($car['model_en']);
                    $description = ($currentLang == 'ar') ? $car['description_ar'] : $car['description_en'];
                    $carCategory = ($currentLang == 'ar') ? $car['category_ar'] : $car['category_en'];
                ?>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                        <div class="car-card h-100 border rounded overflow-hidden shadow-hover">
                            <div class="car-img position-relative overflow-hidden">
                                <img src="<?php echo UPLOADS_PATH .  htmlspecialchars($car['main_image']); ?>" alt="<?php echo $name; ?>" class="img-fluid w-100">
                                <div class="car-overlay d-flex align-items-center justify-content-center">
                                    <a href="<?= createLink('car_details.php', true, $car['car_id']) ?>" class="btn btn-primary view-car-details">
                                        <i class="fas fa-search-plus me-2"></i> <?php echo $lang['view_details']; ?>
                                    </a>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="mb-2"><?php echo $name; ?></h4>
                                <p class="mb-3 text-muted" style="text-align: justify;"><?php echo get_short_content($description, 15); ?></p>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <p class="mb-0"><i class="far fa-calendar-alt text-primary me-2"></i> <?php echo $lang['car_year']; ?> <?php echo htmlspecialchars($car['year']); ?></p>
                                    </div>
                                    <div>
                                        <h5 class="text-primary mb-0">$<?php echo number_format($car['price']); ?></h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
<script>
    // Apply category filter while preserving brand filter
    function applyCategoryFilter(e, categoryValue) {
        e.preventDefault();
        const url = new URL(window.location.href);

        if (categoryValue === 'all') {
            url.searchParams.delete('category');
        } else {
            url.searchParams.set('category', categoryValue);
        }

        window.location.href = url.toString();
        return false;
    }

    // Apply brand filter while preserving category filter
    function applyBrandFilter(e, brandValue) {
        e.preventDefault();
        const url = new URL(window.location.href);

        if (brandValue === 'all') {
            url.searchParams.delete('brand');
        } else {
            url.searchParams.set('brand', brandValue);
        }

        window.location.href = url.toString();
        return false;
    }

    // Legacy function for backward compatibility
    function applyFilter(e, categoryValue) {
        return applyCategoryFilter(e, categoryValue);
    }

    // Text rotation animation
    document.addEventListener('DOMContentLoaded', function() {
        const texts = document.querySelectorAll('.rotating-text');
        let currentIndex = 0;
        const intervalTime = 10000;

        function rotateText() {
            const currentActive = document.querySelector('.rotating-text.active');
            if (currentActive) {
                currentActive.classList.add('fade-out');
                currentActive.classList.remove('active');
            }

            currentIndex = (currentIndex + 1) % texts.length;

            setTimeout(() => {
                texts[currentIndex].classList.remove('fade-out');
                texts[currentIndex].classList.add('active');
            }, 300);
        }

        let rotationInterval = setInterval(rotateText, intervalTime);

        // Add smooth scrolling to filter sections
        const filterButtons = document.querySelectorAll('.brand-filter a, .category-filter a');
        filterButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Add loading effect
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
                
                // Restore original text after a short delay (in case of slow navigation)
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        });
    });
</script>