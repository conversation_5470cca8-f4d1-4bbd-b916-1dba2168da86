<?php
session_start();

include_once '../includes/config.php';
include_once '../includes/functions.php';

$currentLang = getCurrentLanguage();

include_once 'lang/' . $currentLang . '.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_name = trim($_POST['customer_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $car_purchased = trim($_POST['car_purchased'] ?? '');
    $purchase_date = $_POST['purchase_date'] ?? '';
    $rating = intval($_POST['rating'] ?? 0);
    $review_text = trim($_POST['review_text'] ?? '');

    // التحقق من صحة البيانات
    if (empty($customer_name) || empty($email) || empty($review_text) || $rating < 1 || $rating > 5) {
        $_SESSION['review_error'] = ($currentLang == 'ar') ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields';
        header('Location: ' . createLink('submit_review.php'));
        exit;
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['review_error'] = ($currentLang == 'ar') ? 'يرجى إدخال بريد إلكتروني صحيح' : 'Please enter a valid email address';
        header('Location: ' . createLink('submit_review.php'));
        exit;
    }

    try {
        $review_data = [
            'customer_name' => $customer_name,
            'email' => $email,
            'phone' => $phone,
            'car_purchased' => $car_purchased,
            'purchase_date' => $purchase_date,
            'rating' => $rating,
            'review_text' => $review_text
        ];

        addReview($review_data);
        $_SESSION['review_success'] = ($currentLang == 'ar') ? 'تم إرسال رأيك بنجاح. سيتم مراجعته ونشره قريباً.' : 'Your review has been submitted successfully. It will be reviewed and published soon.';
    } catch (Exception $e) {
        $_SESSION['review_error'] = ($currentLang == 'ar') ? 'حدث خطأ أثناء إرسال رأيك. يرجى المحاولة مرة أخرى.' : 'An error occurred while submitting your review. Please try again.';
    }

    header('Location: ' . createLink('submit_review.php'));
    exit;
} else {
    header('Location: ' . createLink('index.php'));
    exit;
}
