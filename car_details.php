<?php
session_start();

include_once '../includes/config.php';
include_once '../includes/functions.php';
$currentLang = getCurrentLanguage();

include_once 'lang/' . $currentLang . '.php';



$id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
$carQuery = 'SELECT 
        c.*,
        cc.category_ar,
        cc.category_en,
        cc.icon
    FROM 
        cars c
    LEFT JOIN 
        car_categories cc ON c.category_id = cc.category_id
    WHERE 
        c.car_id = ?
    LIMIT 1';

$videoQuery = 'SELECT * FROM car_videos WHERE car_id = ? LIMIT 1';
$imagesQuery = 'SELECT * FROM car_images WHERE car_id = ? ORDER BY is_main DESC';

$featuresQuery = 'SELECT * FROM car_features WHERE car_id = ? ORDER BY feature_type_ar';

$car = $db->Fetch($carQuery, [$id]);

if (!$car) {
    header('Location: ' . createLink('cars.php'));
    exit();
}

$video = $db->Fetch($videoQuery, [$id]);
$allImages = $db->FetchAll($imagesQuery, [$id]);
$features = $db->FetchAll($featuresQuery, [$id]);

// Get reviews and rating
$reviews = getCarReviews($id);
$rating = getCarRating($id);

// Get similar cars
$currentBrand = ($currentLang == 'ar') ? $car['brand_ar'] : $car['brand_en'];
$similarCars = getSimilarCars($id, $car['category_id'], $currentBrand, 4);

// Get comparison cars count
$comparisonCars = getComparisonCars();
$comparisonCount = count($comparisonCars);

$mainImages = array_filter($allImages, fn($img) => $img['is_main'] == 1);
$galleryImages = array_filter($allImages, fn($img) => $img['is_main'] == 0);

$organizedFeatures = [];
foreach ($features as $feature) {
    $type = ($currentLang == 'ar') ? $feature['feature_type_ar'] : $feature['feature_type_en'];
    if (!isset($organizedFeatures[$type])) {
        $organizedFeatures[$type] = [];
    }
    $organizedFeatures[$type][] = $feature;
}

$count = $db->RowCountData($carQuery, [$id]);

if ($car) {
    // إنشاء العنوان المخصص
    $car_name = ($currentLang == 'ar') ? $car['brand_ar'] . ' ' . $car['model_ar'] : $car['brand_en'] . ' ' . $car['model_en'];
} else {
    // إعادة التوجيه إذا لم يتم العثور على السيارة
    header('Location: ' . createLink('cars.php'));
    exit();
}

$custom_title = [
    $car_name
];

$GLOBALS['custom_page_title'] = implode(' | ', array_filter($custom_title));

include_once 'templates/header.php';

if ($count > 0) {
    include_once 'templates/car_details.php';
} else {
    header('Location: ' . createLink('cars.php'));
    exit();
}

include_once 'templates/footer.php';
