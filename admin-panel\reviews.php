<?php

// تضمين الملفات المطلوبة
require_once 'includes/config.php';
require_once 'includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

$pageTitle = 'إدارة الآراء';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $review_id = intval($_POST['review_id'] ?? 0);

    if ($review_id > 0) {
        try {
            switch ($action) {
                case 'approve':
                    $db->Update("UPDATE reviews SET status = 'approved' WHERE id = ?", [$review_id]);
                    $success_message = 'تم قبول الرأي بنجاح';
                    break;

                case 'reject':
                    $db->Update("UPDATE reviews SET status = 'rejected' WHERE id = ?", [$review_id]);
                    $success_message = 'تم رفض الرأي';
                    break;

                case 'feature':
                    $db->Update("UPDATE reviews SET is_featured = 1 WHERE id = ?", [$review_id]);
                    $success_message = 'تم إضافة الرأي للمميزة';
                    break;

                case 'unfeature':
                    $db->Update("UPDATE reviews SET is_featured = 0 WHERE id = ?", [$review_id]);
                    $success_message = 'تم إزالة الرأي من المميزة';
                    break;

                case 'delete':
                    $db->Remove("DELETE FROM reviews WHERE id = ?", [$review_id]);
                    $success_message = 'تم حذف الرأي نهائياً';
                    break;
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تنفيذ العملية: ' . $e->getMessage();
        }
    }
}

// جلب الآراء مع الفلترة
$status_filter = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = intval($_GET['page'] ?? 1);
$per_page = 10;
$offset = ($page - 1) * $per_page;

$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(customer_name LIKE ? OR email LIKE ? OR review_text LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب العدد الكلي
$count_sql = "SELECT COUNT(*) FROM reviews $where_clause";
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_reviews = $count_stmt->fetchColumn();
$total_pages = ceil($total_reviews / $per_page);

// جلب الآراء
$sql = "SELECT * FROM reviews $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$reviews = $stmt->fetchAll();

// إحصائيات سريعة
$stats_sql = "SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
    SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured,
    AVG(rating) as avg_rating
FROM reviews";
$stats = $pdo->query($stats_sql)->fetch();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الآراء</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="../submit_review.php" class="btn btn-sm btn-outline-secondary" target="_blank">
                            <i class="fas fa-external-link-alt"></i> عرض صفحة تسجيل الرأي
                        </a>
                    </div>
                </div>
            </div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $stats['total']; ?></h5>
                            <p class="card-text small">إجمالي الآراء</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center bg-warning text-dark">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $stats['pending']; ?></h5>
                            <p class="card-text small">في الانتظار</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $stats['approved']; ?></h5>
                            <p class="card-text small">مقبولة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center bg-danger text-white">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $stats['rejected']; ?></h5>
                            <p class="card-text small">مرفوضة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $stats['featured']; ?></h5>
                            <p class="card-text small">مميزة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center bg-secondary text-white">
                        <div class="card-body">
                            <h5 class="card-title"><?php echo number_format($stats['avg_rating'], 1); ?></h5>
                            <p class="card-text small">متوسط التقييم</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>مقبولة</option>
                                <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>مرفوضة</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search"
                                value="<?php echo htmlspecialchars($search); ?>"
                                placeholder="البحث في الاسم، البريد الإلكتروني، أو نص الرأي">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="reviews.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الآراء -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments me-2"></i>
                        قائمة الآراء (<?php echo $total_reviews; ?> رأي)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($reviews)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد آراء</h5>
                            <p class="text-muted">لم يتم العثور على أي آراء بالمعايير المحددة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>العميل</th>
                                        <th>التقييم</th>
                                        <th>الرأي</th>
                                        <th>السيارة</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reviews as $review): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($review['customer_name']); ?></strong>
                                                    <?php if ($review['is_featured']): ?>
                                                        <span class="badge bg-warning text-dark ms-1">مميز</span>
                                                    <?php endif; ?>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($review['email']); ?></small>
                                                    <?php if ($review['phone']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($review['phone']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="rating-display">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                                    <?php endfor; ?>
                                                    <br>
                                                    <small class="text-muted">(<?php echo $review['rating']; ?>/5)</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="review-text-preview" style="max-width: 200px;">
                                                    <?php echo htmlspecialchars(mb_substr($review['review_text'], 0, 100, 'UTF-8')); ?>
                                                    <?php if (mb_strlen($review['review_text'], 'UTF-8') > 100): ?>
                                                        <span class="text-muted">...</span>
                                                        <br>
                                                        <button class="btn btn-sm btn-link p-0" onclick="showFullReview(<?php echo $review['id']; ?>)">
                                                            عرض كامل
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($review['car_purchased']): ?>
                                                    <strong><?php echo htmlspecialchars($review['car_purchased']); ?></strong>
                                                    <?php if ($review['purchase_date']): ?>
                                                        <br><small class="text-muted"><?php echo date('Y-m-d', strtotime($review['purchase_date'])); ?></small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'bg-warning text-dark',
                                                    'approved' => 'bg-success',
                                                    'rejected' => 'bg-danger'
                                                ];
                                                $status_labels = [
                                                    'pending' => 'في الانتظار',
                                                    'approved' => 'مقبول',
                                                    'rejected' => 'مرفوض'
                                                ];
                                                ?>
                                                <span class="badge <?php echo $status_classes[$review['status']]; ?>">
                                                    <?php echo $status_labels[$review['status']]; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo date('Y-m-d H:i', strtotime($review['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle"
                                                        type="button" data-bs-toggle="dropdown">
                                                        إجراءات
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if ($review['status'] === 'pending'): ?>
                                                            <li>
                                                                <form method="POST" class="d-inline">
                                                                    <input type="hidden" name="action" value="approve">
                                                                    <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                                    <button type="submit" class="dropdown-item text-success">
                                                                        <i class="fas fa-check me-2"></i>قبول
                                                                    </button>
                                                                </form>
                                                            </li>
                                                            <li>
                                                                <form method="POST" class="d-inline">
                                                                    <input type="hidden" name="action" value="reject">
                                                                    <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                                    <button type="submit" class="dropdown-item text-danger">
                                                                        <i class="fas fa-times me-2"></i>رفض
                                                                    </button>
                                                                </form>
                                                            </li>
                                                        <?php endif; ?>

                                                        <?php if ($review['status'] === 'approved'): ?>
                                                            <?php if (!$review['is_featured']): ?>
                                                                <li>
                                                                    <form method="POST" class="d-inline">
                                                                        <input type="hidden" name="action" value="feature">
                                                                        <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                                        <button type="submit" class="dropdown-item text-warning">
                                                                            <i class="fas fa-star me-2"></i>جعل مميز
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            <?php else: ?>
                                                                <li>
                                                                    <form method="POST" class="d-inline">
                                                                        <input type="hidden" name="action" value="unfeature">
                                                                        <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                                        <button type="submit" class="dropdown-item">
                                                                            <i class="fas fa-star-half-alt me-2"></i>إلغاء التمييز
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            <?php endif; ?>
                                                        <?php endif; ?>

                                                        <li>
                                                            <hr class="dropdown-divider">
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item" onclick="showReviewDetails(<?php echo $review['id']; ?>)">
                                                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <form method="POST" class="d-inline"
                                                                onsubmit="return confirm('هل أنت متأكد من حذف هذا الرأي نهائياً؟')">
                                                                <input type="hidden" name="action" value="delete">
                                                                <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                                <button type="submit" class="dropdown-item text-danger">
                                                                    <i class="fas fa-trash me-2"></i>حذف نهائي
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">السابق</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </main>
    </div>
</div>

<!-- Modal لعرض تفاصيل الرأي -->
<div class="modal fade" id="reviewDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الرأي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reviewDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<script>
    function showReviewDetails(reviewId) {
        // جلب تفاصيل الرأي عبر AJAX
        fetch(`ajax/get_review_details.php?id=${reviewId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const review = data.review;
                    const content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>معلومات العميل</h6>
                            <p><strong>الاسم:</strong> ${review.customer_name}</p>
                            <p><strong>البريد الإلكتروني:</strong> ${review.email}</p>
                            <p><strong>الهاتف:</strong> ${review.phone || 'غير محدد'}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>معلومات السيارة</h6>
                            <p><strong>السيارة:</strong> ${review.car_purchased || 'غير محدد'}</p>
                            <p><strong>تاريخ الشراء:</strong> ${review.purchase_date || 'غير محدد'}</p>
                            <p><strong>التقييم:</strong> ${review.rating}/5 نجوم</p>
                        </div>
                    </div>
                    <hr>
                    <h6>نص الرأي</h6>
                    <div class="bg-light p-3 rounded">
                        ${review.review_text}
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الحالة:</strong> 
                                <span class="badge ${review.status === 'approved' ? 'bg-success' : review.status === 'rejected' ? 'bg-danger' : 'bg-warning text-dark'}">
                                    ${review.status === 'approved' ? 'مقبول' : review.status === 'rejected' ? 'مرفوض' : 'في الانتظ��ر'}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ الإرسال:</strong> ${review.created_at}</p>
                        </div>
                    </div>
                `;
                    document.getElementById('reviewDetailsContent').innerHTML = content;
                    new bootstrap.Modal(document.getElementById('reviewDetailsModal')).show();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في جلب تفاصيل الرأي');
            });
    }

    function showFullReview(reviewId) {
        showReviewDetails(reviewId);
    }
</script>

<?php include 'includes/footer.php'; ?>