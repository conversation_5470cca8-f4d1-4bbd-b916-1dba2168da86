<?php
$currentLang = getCurrentLanguage();

// تضمين دوال الآراء مع التعامل مع الأخطاء
$real_reviews = [];
$reviews_stats = [
    'total_reviews' => 0,
    'average_rating' => 0,
    'approved_reviews' => 0,
    'featured_reviews' => 0,
    'satisfaction_rate' => 0
];
$has_real_reviews = false;

try {
    // جلب الآراء الحقيقية - الدوال متوفرة الآن في functions.php
    if (function_exists('getApprovedReviews')) {
        $real_reviews = getApprovedReviews(5, false);
    }

    if (function_exists('getReviewsStats')) {
        $reviews_stats = getReviewsStats();
    }

    if (function_exists('hasRealReviews')) {
        $has_real_reviews = hasRealReviews();
    }
} catch (Exception $e) {
    error_log("Error loading reviews: " . $e->getMessage());
    // استخدام القيم الافتراضية في حالة الخطأ
}
?>

<section class="hero-section position-relative" style="padding-top: 100px;">

    <div class="animated-bg" data-url="<?php echo ASSETS_PATH; ?>" style="<?php echo ($currentLang == 'ar') ? 'transform: rotateY(-180deg);' : ''; ?>"></div>
    <div class="container" style="z-index: 2;">
        <div class="row min-vh-80 align-items-center">
            <div class="col-lg-12 text-center text-lg-start" data-aos="fade-right">
                <h2 class="fw-bold mb-3 text-white"><?php echo ($currentLang == 'ar') ? SITE_NAME_AR : SITE_NAME_EN; ?></h2>
                <div class="text-rotator">
                    <p class="lead mb-4 text-white rotating-text active"><?php echo $lang['hero_subtitle1']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle2']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle3']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle4']; ?></p>
                </div>
                <a href="<?php echo createLink('cars.php'); ?>" class="btn btn-primary btn-lg">
                    <?php echo $lang['hero_button']; ?>
                    <i class="fas fa-arrow-<?php echo ($currentLang == 'ar') ? 'left' : 'right'; ?> ms-2"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="particles" id="particles"></div>
    <svg data-name="VIDEO GRADIENT" xmlns="http://www.w3.org/2000/svg" class="home-hero__gradient" role="presentation">
        <defs data-v-1bfe21c6="">
            <linearGradient id="DesktopGradient_svg__a" x1=".5" y1=".129" x2=".5" y2=".708" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-opacity="0"></stop>
                <stop offset=".364" stop-opacity=".424"></stop>
                <stop offset=".64" stop-color="#030b11" stop-opacity=".733"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
            <linearGradient id="DesktopGradient_svg__b" x1=".5" y1=".129" x2=".5" y2=".5" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-color="#05141f" stop-opacity="0"></stop>
                <stop offset=".226" stop-color="#05141f" stop-opacity=".082"></stop>
                <stop offset=".512" stop-color="#05141f" stop-opacity=".271"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
        </defs>
        <rect fill="url(#DesktopGradient_svg__a)" width="100%" height="204"></rect>
        <rect fill="url(#DesktopGradient_svg__b)" y="72" width="100%" height="132"></rect>
    </svg>
</section>

<!-- About Section -->
<section id="about" class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0" data-aos="fade-right">
                <img src="<?php echo ASSETS_PATH ?>images/about-us.png" alt="About Us" class="img-fluid">
            </div>
            <div class="col-lg-6" data-aos="fade-left" data-aos-delay="200">
                <h2 class="section-title position-relative pb-3 mb-4">
                    <span class="text-dark"><?php echo $lang['about_title']; ?></span>
                </h2>
                <p class="mb-4" style="text-align: justify;"><?php echo $lang['about_content']; ?></p>
                <div class="d-flex align-items-center mb-3">
                    <div class="d-flex flex-shrink-0 align-items-center justify-content-center bg-primary rounded-circle" style="width: 50px; height: 50px;">
                        <i class="fa fa-check text-white"></i>
                    </div>
                    <div class="ms-3">
                        <h5 class="mb-0"><?php echo $lang['about_1_title']; ?></h5>
                    </div>
                </div>
                <div class="d-flex align-items-center mb-3">
                    <div class="d-flex flex-shrink-0 align-items-center justify-content-center bg-primary rounded-circle" style="width: 50px; height: 50px;">
                        <i class="fa fa-check text-white"></i>
                    </div>
                    <div class="ms-3">
                        <h5 class="mb-0"><?php echo $lang['about_2_title']; ?></h5>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <div class="d-flex flex-shrink-0 align-items-center justify-content-center bg-primary rounded-circle" style="width: 50px; height: 50px;">
                        <i class="fa fa-check text-white"></i>
                    </div>
                    <div class="ms-3">
                        <h5 class="mb-0"><?php echo $lang['about_3_title']; ?></h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section id="services" class="py-5">
    <div class="container">
        <div class="text-center mx-auto mb-5" style="max-width: 600px;" data-aos="fade-up">
            <h2 class="section-title position-relative pb-3 mb-4">
                <span class="text-dark"><?php echo $lang['services_title']; ?></span>
            </h2>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                <div class="service-card h-100 p-5 border rounded shadow-sm">
                    <div class="service-icon mt-1 mb-4">
                        <i class="fas fa-car fa-3x text-white"></i>
                    </div>
                    <h4 class="mb-3"><?php echo $lang['service_1_title']; ?></h4>
                    <p><?php echo $lang['service_1_desc']; ?></p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                <div class="service-card h-100 p-5 border rounded shadow-sm">
                    <div class="service-icon mt-1 mb-4">
                        <i class="fas fa-truck fa-3x text-white"></i>
                    </div>
                    <h4 class="mb-3"><?php echo $lang['service_2_title']; ?></h4>
                    <p><?php echo $lang['service_2_desc']; ?></p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                <div class="service-card h-100 p-5 border rounded shadow-sm">
                    <div class="service-icon mt-1 mb-4">
                        <i class="fas fa-tools fa-3x text-white"></i>
                    </div>
                    <h4 class="mb-3"><?php echo $lang['service_3_title']; ?></h4>
                    <p><?php echo $lang['service_3_desc']; ?></p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Cars Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row g-4">
            <?php
            if (empty(getCars())):
            ?>
                <div class="col-12 text-center">
                    <div class="alert alert-info" role="alert">
                        <?php echo ($currentLang == 'ar') ? 'لا توجد سيارات في هذه الفئة حالياً.' : 'No cars in this category at the moment.'; ?>
                    </div>
                </div>
                <?php
            else:
                foreach (getCars(3, 1) as $index => $car):
                    $delay = ($index % 3 + 1) * 100;
                    $name = ($currentLang == 'ar') ? htmlspecialchars($car['brand_ar']) . ' ' . htmlspecialchars($car['model_ar']) : htmlspecialchars($car['brand_en']) . ' ' . htmlspecialchars($car['model_en']);

                    $description = ($currentLang == 'ar') ? htmlspecialchars($car['description_ar']) : htmlspecialchars($car['description_en']);
                ?>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                        <div class="car-card h-100 border rounded overflow-hidden shadow-hover">
                            <div class="car-img position-relative overflow-hidden">
                                <img src="<?php echo UPLOADS_PATH .  htmlspecialchars($car['main_image']); ?>" alt="<?php echo $name; ?>" class="img-fluid w-100">
                                <div class="car-overlay d-flex align-items-center justify-content-center">
                                    <a href="<?= createLink('car_details.php', true, $car['car_id']) ?>" class="btn btn-primary view-car-details">
                                        <i class="fas fa-search-plus me-2"></i> <?php echo $lang['view_details']; ?>
                                    </a>
                                </div>
                            </div>
                            <div class="p-4">
                                <h4 class="mb-2"><?php echo $name; ?></h4>
                                <p class="mb-3 text-muted" style="text-align: justify;"><?php echo get_short_content($description, 15); ?></p>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <p class="mb-0"><i class="far fa-calendar-alt text-primary me-2"></i> <?php echo $lang['car_year']; ?> <?php echo htmlspecialchars($car['year']); ?></p>
                                    </div>
                                    <div>
                                        <h5 class="text-primary mb-0">$<?php echo number_format($car['price']); ?></h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>
</section>
<!-- Contact Info Section -->

<!-- Main Carousel Section -->
<div class="main-container" data-aos="fade-up">

    <!-- Main Carousel Section -->
    <section class="carousel-background-section position-relative overflow-hidden" style="height: 50vh;">
        <!-- Background -->
        <div class="background-overlay position-absolute w-100 h-100"></div>
        <?php
        if ($currentLang == 'ar'):
            $prevArrow = 'right';
            $nextArrow = 'left';
        else:
            $prevArrow = 'left';
            $nextArrow = 'right';

        endif; ?>
        <!-- Custom Navigation Arrows -->
        <button class="custom-prev-arrow position-absolute btn btn-light rounded-circle" id="prevArrow">
            <i class="bi bi-chevron-<?php echo $prevArrow; ?>"></i>
        </button>
        <button class="custom-next-arrow position-absolute btn btn-light rounded-circle" id="nextArrow">
            <i class="bi bi-chevron-<?php echo $nextArrow; ?>"></i>
        </button>
    </section>

    <!-- Carousel -->
    <div class="kia-carousel position-relative" style="margin-top: -50vh;">
        <!-- Slide 1 -->
        <div class="slide">
            <div class="slide-content text-center">
                <div class="car-image mb-4">
                    <img src="<?php echo UPLOADS_PATH . 'cars/Telluride.avif' ?>" alt="2025 Telluride" class="img-fluid">

                </div>
                <div class="car-info-slick text-dark">
                    <div class="car-year-slick fs-5 fw-light mb-3">2025</div>
                    <h2 class="car-model-slick display-1 fw-bold mb-3">Telluride</h2>
                    <!--<div class="car-specs-slick row justify-content-center mb-4">
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="spec-item text-center">
                                <div class="spec-label small text-light-emphasis mb-2">STARTING AT</div>
                                <div class="spec-value fs-2 fw-bold">$36,390<sup class="fs-5">1</sup></div>
                            </div>
                        </div>
                    </div>-->
                    <div class="action-buttons d-flex justify-content-center gap-3 flex-wrap">
                        <button class="btn btn-dark btn-lg px-4">Learn more</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2 -->
        <div class="slide">
            <div class="slide-content text-center">
                <div class="car-image mb-4">
                    <img src="<?php echo UPLOADS_PATH . 'cars/Soul.avif' ?>" alt="2025 Soul" class="img-fluid">
                </div>
                <div class="car-info-slick text-dark">
                    <div class="car-year-slick fs-5 fw-light mb-3">2025</div>
                    <h2 class="car-model-slick display-1 fw-bold mb-3">Soul</h2>
                    <!--<div class="car-specs-slick row justify-content-center mb-4">
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="spec-item text-center">
                                <div class="spec-label small text-light-emphasis mb-2">STARTING AT</div>
                                <div class="spec-value fs-2 fw-bold">$20,490<sup class="fs-5">1</sup></div>
                            </div>
                        </div>
                    </div>-->
                    <div class="action-buttons d-flex justify-content-center gap-3 flex-wrap">
                        <button class="btn btn-dark btn-lg px-4">Learn more</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3 -->
        <div class="slide">
            <div class="slide-content text-center">
                <div class="car-image mb-4">
                    <img src="<?php echo UPLOADS_PATH . 'cars/Carnival.avif' ?>" alt="2025 Carnival" class="img-fluid">
                </div>
                <div class="car-info-slick text-dark">
                    <div class="car-year-slick fs-5 fw-light mb-3">2026</div>
                    <h2 class="car-model-slick display-1 fw-bold mb-3">Carnival MPV</h2>
                    <!--<div class="car-specs-slick row justify-content-center mb-4">
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="spec-item text-center">
                                <div class="spec-label small text-light-emphasis mb-2">STARTING AT</div>
                                <div class="spec-value fs-2 fw-bold">$35,300<sup class="fs-5">1</sup></div>
                            </div>
                        </div>
                    </div>-->
                    <div class="action-buttons d-flex justify-content-center gap-3 flex-wrap">
                        <button class="btn btn-dark btn-lg px-4">Learn more</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4 -->
        <div class="slide">
            <div class="slide-content text-center">
                <div class="car-image mb-4">
                    <img src="<?php echo UPLOADS_PATH . 'cars/Seltos.avif' ?>" alt="2025 Carnival" class="img-fluid">
                </div>
                <div class="car-info-slick text-dark">
                    <div class="car-year-slick fs-5 fw-light mb-3">2026</div>
                    <h2 class="car-model-slick display-1 fw-bold mb-3">Seltos</h2>
                    <!--<div class="car-specs-slick row justify-content-center mb-4">
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="spec-item text-center">
                                <div class="spec-label small text-light-emphasis mb-2">STARTING AT</div>
                                <div class="spec-value fs-2 fw-bold">$35,300<sup class="fs-5">1</sup></div>
                            </div>
                        </div>
                    </div>-->
                    <div class="action-buttons d-flex justify-content-center gap-3 flex-wrap">
                        <button class="btn btn-dark btn-lg px-4">Learn more</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5 -->
        <div class="slide">
            <div class="slide-content text-center">
                <div class="car-image mb-4">
                    <img src="<?php echo UPLOADS_PATH . 'cars/Sportage.avif' ?>" alt="2025 Carnival" class="img-fluid">
                </div>
                <div class="car-info-slick text-dark">
                    <div class="car-year-slick fs-5 fw-light mb-3">2026</div>
                    <h2 class="car-model-slick display-1 fw-bold mb-3">Sportage</h2>
                    <!--<div class="car-specs-slick row justify-content-center mb-4">
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="spec-item text-center">
                                <div class="spec-label small text-light-emphasis mb-2">STARTING AT</div>
                                <div class="spec-value fs-2 fw-bold">$35,300<sup class="fs-5">1</sup></div>
                            </div>
                        </div>
                    </div>-->
                    <div class="action-buttons d-flex justify-content-center gap-3 flex-wrap">
                        <button class="btn btn-dark btn-lg px-4">Learn more</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6 -->
        <div class="slide">
            <div class="slide-content text-center">
                <div class="car-image mb-4">
                    <img src="<?php echo UPLOADS_PATH . 'cars/Sorento.avif' ?>" alt="2025 Carnival" class="img-fluid">
                </div>
                <div class="car-info-slick text-dark">
                    <div class="car-year-slick fs-5 fw-light mb-3">2025</div>
                    <h2 class="car-model-slick display-1 fw-bold mb-3">Sorento</h2>
                    <!--<div class="car-specs-slick row justify-content-center mb-4">
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                            <div class="spec-item text-center">
                                <div class="spec-label small text-light-emphasis mb-2">STARTING AT</div>
                                <div class="spec-value fs-2 fw-bold">$35,300<sup class="fs-5">1</sup></div>
                            </div>
                        </div>
                    </div>-->
                    <div class="action-buttons d-flex justify-content-center gap-3 flex-wrap">
                        <button class="btn btn-dark btn-lg px-4">Learn more</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Reviews Section -->
<section class="customer-reviews-section position-relative overflow-hidden" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); padding: 50px 0;">
    <div class="container">
        <!-- Section Header -->
        <div class="text-center mx-auto mb-5" style="max-width: 700px; margin-bottom: 35px !important;">
            <div class="d-flex align-items-center justify-content-center mb-4">
                <h2 class="section-title position-relative pb-0 mb-0">
                    <span class="text-dark"><?php echo $lang['reviews_title']; ?></span>
                </h2>
            </div>
            <p class="text-muted lead"><?php echo $lang['reviews_subtitle']; ?></p>
        </div>

        <!-- Reviews Carousel -->
        <div class="reviews-carousel">
            <?php
            // عرض الآراء من قاعدة البيانات فقط
            if (!empty($real_reviews)):
                foreach ($real_reviews as $index => $review):
                    // تنسيق التاريخ
                    $formatted_date = '';
                    if (!empty($review['purchase_date'])) {
                        if (function_exists('formatReviewDate')) {
                            $formatted_date = formatReviewDate($review['purchase_date'], $currentLang);
                        } else {
                            $formatted_date = date('Y-m-d', strtotime($review['purchase_date']));
                        }
                    } elseif (!empty($review['created_at'])) {
                        if (function_exists('formatReviewDate')) {
                            $formatted_date = formatReviewDate($review['created_at'], $currentLang);
                        } else {
                            $formatted_date = date('Y-m-d', strtotime($review['created_at']));
                        }
                    }

                    // اقتطاع النص للمعاينة
                    if (function_exists('truncateReviewText')) {
                        $preview_text = truncateReviewText($review['review_text'], 120, $currentLang);
                    } else {
                        $preview_text = mb_strlen($review['review_text'], 'UTF-8') > 120 ?
                            mb_substr($review['review_text'], 0, 120, 'UTF-8') . '...' : $review['review_text'];
                    }
                    $is_truncated = mb_strlen($review['review_text'], 'UTF-8') > 120;
            ?>
                    <div class="review-card">
                        <div class="card h-100 border-0 shadow-sm position-relative">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <h5 class="mb-1 fw-bold"><?php echo htmlspecialchars($review['customer_name']); ?></h5>
                                        <div class="stars mb-2">
                                            <?php
                                            if (function_exists('generateStarsHTML')) {
                                                echo generateStarsHTML($review['rating'], true);
                                            } else {
                                                // عرض النجوم بطريقة بسيطة
                                                $rating = $review['rating'];
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $rating) {
                                                        echo '<i class="fas fa-star text-warning"></i>';
                                                    } elseif ($i - 0.5 <= $rating) {
                                                        echo '<i class="fas fa-star-half-alt text-warning"></i>';
                                                    } else {
                                                        echo '<i class="far fa-star text-warning"></i>';
                                                    }
                                                }
                                                echo '<span class="ms-2 text-muted small">(' . $rating . ')</span>';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <blockquote class="blockquote mb-4">
                                    <p class="mb-0 text-dark review-text" style="text-align: justify; line-height: 1.7;font-size: 18px;" data-full-text="<?php echo htmlspecialchars($review['review_text']); ?>">
                                        <span class="review-preview"><?php echo $preview_text; ?></span>
                                        <?php if ($is_truncated): ?>
                                            <button class="btn btn-primary btn-sm read-more-btn"
                                                data-customer="<?php echo htmlspecialchars($review['customer_name']); ?>"
                                                data-rating="<?php echo $review['rating']; ?>"
                                                data-car="<?php echo htmlspecialchars($review['car_purchased'] ?? ($currentLang == 'ar' ? 'غير محدد' : 'Not specified')); ?>"
                                                data-date="<?php echo htmlspecialchars($formatted_date); ?>">
                                                <span><?php echo ($currentLang == 'ar') ? 'قراءة المزيد' : 'Read More'; ?></span>
                                            </button>
                                        <?php endif; ?>
                                    </p>
                                </blockquote>
                                <div class="review-meta">
                                    <div class="row g-2">
                                        <div class="col-sm-6">
                                            <div class="d-flex align-items-center">
                                                <div class="meta-icon me-2">
                                                    <i class="fas fa-car text-primary"></i>
                                                </div>
                                                <small class="text-muted fw-medium">
                                                    <?php echo htmlspecialchars($review['car_purchased'] ?? ($currentLang == 'ar' ? 'غير محدد' : 'Not specified')); ?>
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="d-flex align-items-center">
                                                <div class="meta-icon me-2">
                                                    <i class="fas fa-calendar-alt text-primary"></i>
                                                </div>
                                                <small class="text-muted fw-medium"><?php echo $formatted_date; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php
                endforeach;
            else:
                // عرض رسالة "لا توجد آراء" عندما تكون قائمة الآراء فارغة
                ?>
                <div class="col-12 text-center py-5">
                    <div class="no-reviews-message">
                        <div class="no-reviews-icon mb-4">
                            <i class="fas fa-comments text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                        </div>
                        <h4 class="text-muted mb-3">
                            <?php echo ($currentLang == 'ar') ? 'لا توجد آراء لغاية الآن' : 'No reviews yet'; ?>
                        </h4>
                        <p class="text-muted mb-4">
                            <?php echo ($currentLang == 'ar') ? 'كن أول من يشارك تجربته مع خدماتنا' : 'Be the first to share your experience with our services'; ?>
                        </p>
                        <a href="<?php echo createLink('submit_review.php'); ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-star me-2"></i>
                            <?php echo ($currentLang == 'ar') ? 'اكتب أول رأي' : 'Write First Review'; ?>
                        </a>
                    </div>
                </div>
            <?php
            endif; // نهاية شرط عرض الآراء أو رسالة "لا توجد آراء"
            ?>
        </div>

        <!-- Reviews Statistics -->
        <div class="row mt-5">
            <?php
            // تحديد الإحصائيات المراد عرضها - استخدام البيانات الحقيقية فقط
            $display_stats = [
                'average_rating' => !empty($real_reviews) ? $reviews_stats['average_rating'] : 0,
                'total_reviews' => !empty($real_reviews) ? $reviews_stats['approved_reviews'] : 0,
                'satisfaction_rate' => !empty($real_reviews) ? $reviews_stats['satisfaction_rate'] : 0,
                'happy_customers' => !empty($real_reviews) ? ($reviews_stats['approved_reviews'] * 2.4) : 0
            ];
            ?>
            <div class="col-md-3 col-6 text-center mb-4">
                <div class="stat-item p-4 bg-white rounded-3 shadow-sm h-100">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-star-half-alt text-warning" style="font-size: 2.5rem;"></i>
                    </div>
                    <div class="stat-number display-4 fw-bold text-primary mb-2">
                        <?php echo number_format($display_stats['average_rating'], 1); ?>
                    </div>
                    <div class="stat-label text-muted fw-semibold"><?php echo $lang['average_rating']; ?></div>
                    <div class="stars mt-2">
                        <?php
                        $rating = $display_stats['average_rating'];
                        for ($i = 1; $i <= 5; $i++) {
                            if ($i <= $rating) {
                                echo '<i class="fas fa-star text-warning"></i>';
                            } elseif ($i - 0.5 <= $rating) {
                                echo '<i class="fas fa-star-half-alt text-warning"></i>';
                            } else {
                                echo '<i class="far fa-star text-warning"></i>';
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 text-center mb-4">
                <div class="stat-item p-4 bg-white rounded-3 shadow-sm h-100">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-comments text-info" style="font-size: 2.5rem;"></i>
                    </div>
                    <div class="stat-number display-4 fw-bold text-primary mb-2">
                        <?php
                        if ($display_stats['total_reviews'] > 0) {
                            echo $display_stats['total_reviews'] > 100 ? $display_stats['total_reviews'] . '+' : $display_stats['total_reviews'];
                        } else {
                            echo '0';
                        }
                        ?>
                    </div>
                    <div class="stat-label text-muted fw-semibold"><?php echo $lang['total_reviews']; ?></div>
                </div>
            </div>
            <div class="col-md-3 col-6 text-center mb-4">
                <div class="stat-item p-4 bg-white rounded-3 shadow-sm h-100">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-thumbs-up text-success" style="font-size: 2.5rem;"></i>
                    </div>
                    <div class="stat-number display-4 fw-bold text-primary mb-2">
                        <?php echo $display_stats['satisfaction_rate']; ?>%
                    </div>
                    <div class="stat-label text-muted fw-semibold"><?php echo $lang['satisfaction_rate']; ?></div>
                </div>
            </div>
            <div class="col-md-3 col-6 text-center mb-4">
                <div class="stat-item p-4 bg-white rounded-3 shadow-sm h-100">
                    <div class="stat-icon mb-3">
                        <i class="fas fa-users text-primary" style="font-size: 2.5rem;"></i>
                    </div>
                    <div class="stat-number display-4 fw-bold text-primary mb-2">
                        <?php
                        if ($display_stats['happy_customers'] > 0) {
                            echo round($display_stats['happy_customers']) . '+';
                        } else {
                            echo '0';
                        }
                        ?>
                    </div>
                    <div class="stat-label text-muted fw-semibold"><?php echo $lang['happy_customers']; ?></div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-5">
            <div class="cta-wrapper p-5 bg-white rounded-4 shadow-lg position-relative overflow-hidden">
                <div class="cta-bg-decoration position-absolute">
                    <i class="fas fa-heart text-danger opacity-10"></i>
                </div>
                <div class="row align-items-center">
                    <div class="col-lg-8 text-lg-start text-center">
                        <div class="d-flex align-items-center justify-content-lg-start justify-content-center mb-3">
                            <div class="cta-icon me-3">
                                <i class="fas fa-handshake text-primary"></i>
                            </div>
                            <h4 class="mb-0 fw-bold"><?php echo $lang['reviews_cta_title']; ?></h4>
                        </div>
                        <p class="text-muted mb-lg-0 mb-4"><?php echo $lang['reviews_cta_subtitle']; ?></p>
                    </div>
                    <div class="col-lg-4 text-lg-end text-center">
                        <a href="<?php echo createLink('submit_review.php'); ?>" class="btn btn-primary btn-lg px-5 py-3 rounded-pill shadow-sm">
                            <i class="fas fa-paper-plane me-2"></i>
                            <?php echo $lang['share_experience']; ?>
                            <i class="fas fa-arrow-<?php echo ($currentLang == 'ar') ? 'left' : 'right'; ?> ms-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-primary text-white border-0">
                <div class="d-flex align-items-center">
                    <div class="modal-avatar me-3">
                        <div class="avatar-icon rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    </div>
                    <div>
                        <h5 class="modal-title mb-1" id="reviewModalLabel"></h5>
                        <div class="modal-stars mb-0" id="modalStars"></div>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="meta-icon me-2">
                                <i class="fas fa-car text-primary"></i>
                            </div>
                            <small class="text-muted fw-medium" id="modalCar"></small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="meta-icon me-2">
                                <i class="fas fa-calendar-alt text-primary"></i>
                            </div>
                            <small class="text-muted fw-medium" id="modalDate"></small>
                        </div>
                    </div>
                </div>
                <div class="review-full-text">
                    <blockquote class="blockquote">
                        <p class="mb-0 text-dark" style="text-align: justify; line-height: 1.8; font-size: 1.1rem;" id="modalReviewText"></p>
                    </blockquote>
                </div>
                <div class="d-flex align-items-center mt-4 p-3 bg-light rounded">
                    <i class="fas fa-shield-alt text-success me-2"></i>
                    <small class="text-success fw-semibold"><?php echo $lang['verified_purchase']; ?></small>
                </div>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    <?php echo ($currentLang == 'ar') ? 'إغلاق' : 'Close'; ?>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const heroSection = document.querySelector('.animated-bg');
        const textRotatorContainer = document.querySelector('.text-rotator');
        const rotatingTexts = document.querySelectorAll('.rotating-text');

        const heroImages = [
            heroSection.getAttribute('data-url') + 'images/tech.webp',
            heroSection.getAttribute('data-url') + 'images/bumpy.webp',
            heroSection.getAttribute('data-url') + 'images/stand_out.webp',
            heroSection.getAttribute('data-url') + 'images/hero-summer.jpg',
            heroSection.getAttribute('data-url') + 'images/Web-Banner-1-min.jpg',
            heroSection.getAttribute('data-url') + 'images/hero5.png',
        ];

        let currentIndex = 0;
        let rotationInterval;
        const rotationTime = 10000;

        function goToSlide(index) {
            currentIndex = index;

            heroSection.style.backgroundImage = `url(${heroImages[currentIndex]})`;
            heroSection.style.transition = 'background-image 1s ease-in-out';

            rotatingTexts.forEach((text, i) => {
                text.classList.toggle('active', i === currentIndex);
            });

            restartRotation();
        }

        function nextSlide() {
            const newIndex = (currentIndex + 1) % heroImages.length;
            goToSlide(newIndex);
        }

        function restartRotation() {
            clearInterval(rotationInterval);
            rotationInterval = setInterval(nextSlide, rotationTime);
        }

        function init() {
            const randomNumber = Math.floor(Math.random() * 4);
            heroSection.style.backgroundImage = `url(${heroImages[randomNumber]})`;

            rotationInterval = setInterval(nextSlide, rotationTime);
        }

        init();

        const particlesContainer = document.getElementById('particles');
        const particleCount = window.innerWidth < 768 ? 30 : 50;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.classList.add('particle');

            const size = Math.random() * 5 + 2;
            const posX = Math.random() * window.innerWidth;
            const duration = Math.random() * 20 + 10;
            const delay = Math.random() * 5;

            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.left = `${posX}px`;
            particle.style.bottom = `-10px`;
            particle.style.animationDuration = `${duration}s`;
            particle.style.animationDelay = `${delay}s`;
            particle.style.opacity = Math.random() * 0.5 + 0.1;

            particlesContainer.appendChild(particle);
        }

        // Read More functionality for reviews
        const readMoreBtns = document.querySelectorAll('.read-more-btn');
        const reviewModal = new bootstrap.Modal(document.getElementById('reviewModal'));

        readMoreBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                const reviewText = this.closest('.review-text');
                const fullText = reviewText.getAttribute('data-full-text');
                const customer = this.getAttribute('data-customer');
                const rating = this.getAttribute('data-rating');
                const car = this.getAttribute('data-car');
                const date = this.getAttribute('data-date');

                // Update modal content
                document.getElementById('reviewModalLabel').textContent = customer;
                document.getElementById('modalReviewText').textContent = fullText;
                document.getElementById('modalCar').textContent = car;
                document.getElementById('modalDate').textContent = date;

                // Generate stars for modal
                const starsContainer = document.getElementById('modalStars');
                const ratingNum = parseFloat(rating);
                let starsHTML = '';

                for (let i = 1; i <= 5; i++) {
                    if (i <= ratingNum) {
                        starsHTML += '<i class="fas fa-star text-warning"></i>';
                    } else if (i - 0.5 <= ratingNum) {
                        starsHTML += '<i class="fas fa-star-half-alt text-warning"></i>';
                    } else {
                        starsHTML += '<i class="far fa-star text-warning"></i>';
                    }
                }
                starsHTML += `<span class="ms-2 text-light small">(${rating})</span>`;
                starsContainer.innerHTML = starsHTML;

                // Show modal
                reviewModal.show();
            });
        });


    });
</script>