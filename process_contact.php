<?php
session_start();

include_once '../includes/config.php';
include_once '../includes/functions.php';

$currentLang = getCurrentLanguage();

include_once 'lang/' . $currentLang . '.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_STRING);
    $message = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_STRING);

    if (empty($name) || empty($email) || empty($phone) || empty($message)) {
        $_SESSION['contact_error'] = ($currentLang == 'ar') ? 'يرجى ملء جميع الحقول المطلوبة.' : 'Please fill all required fields.';
        header('Location: ' . createLink('contact.php'));
        exit;
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['contact_error'] = ($currentLang == 'ar') ? 'يرجى إدخال بريد إلكتروني صحيح.' : 'Please enter a valid email address.';
        header('Location: ' . createLink('contact.php'));
        exit;
    }

    $to = COMPANY_EMAIL;
    $subject = 'رسالة جديدة من موقع ديار الكرم';
    $body = "الاسم: $name\n";
    $body .= "البريد الإلكتروني: $email\n";
    $body .= "الهاتف: $phone\n";
    $body .= "الرسالة:\n$message";
    $headers = "From: $email";

    if (mail($to, $subject, $body, $headers)) {
        $_SESSION['contact_success'] = ($currentLang == 'ar') ? 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.' : 'Your message has been sent successfully. We will contact you soon.';
    } else {
        $_SESSION['contact_error'] = ($currentLang == 'ar') ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.';
    }

    header('Location: ' . createLink('contact.php'));

    exit;
} else {
    header('Location: index.php');
    exit;
}
