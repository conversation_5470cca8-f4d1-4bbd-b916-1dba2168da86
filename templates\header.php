<?php
ob_start();
$currentLang = getCurrentLanguage();
$direction = getDirection();
$oppositeLang = getOppositeLanguage();
$oppositeLanguageName = getOppositeLanguageName();
$logo =  ASSETS_PATH . 'images/' . $settings['logo'];
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $direction; ?>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        <?php echo dynamicPageTitle(); ?>
    </title>



    <meta name="description" content="<?php echo ($currentLang == 'ar') ? $settings['about_ar'] : $settings['about_en']; ?>">

    <meta name="keywords" content="سيارات كيا, كيا للبيع, أسعار سيارات كيا, كيا جديدة, كيا مستعملة, معارض كيا, تقسيط سيارات كيا, عروض كيا, وكيل كيا, كيا سيراتو, كيا سبورتاج, كيا كارينز, كيا بيكانتو, كيا سول, كيا سورينتو, كيا اوبتيما, كيا نيرو, كيا سيفيا, كيا ستينجر, سيارات كيا 2024, سيارات كيا 2025, كيا وكيل معتمد, أفضل أسعار كيا, خصومات كيا, تأمين سيارات كيا, ضمان سيارات كيا, كاش أو تقسيط كيا">
    <meta name="author" content="Diyar Alkaram">

    <meta property="og:title" content="<?php echo dynamicPageTitle(); ?>">
    <meta property="og:description" content="<?php echo ($currentLang == 'ar') ? $settings['about_ar'] : $settings['about_en']; ?>">
    <meta property="og:image" content="<?php echo ASSETS_PATH . 'images/' . $settings['logo']; ?>">
    <meta property="og:url" content="<?php echo getCurrentUrl(false); ?>">

    <meta property="og:type" content="website">
    <meta name="robots" content="index, follow">
    <meta name="google-site-verification" content="QbqU8836D18tAnx8J3JEzcIyoEtzip8z0X4d5Dwj0WY" />
    <?php if ($currentLang == 'ar'): ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link rel="icon" href="<?php echo ASSETS_PATH; ?>images/favicon.png" type="image/svg+xml">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_PATH; ?>images/logo.png">

    <link
        rel="stylesheet"
        type="text/css"
        href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css" />
    <link
        rel="stylesheet"
        type="text/css"
        href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css" />

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>css/style.css">

    <?php if ($currentLang == 'ar'): ?>
        <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>css/slick-rtl.css">
    <?php else: ?>
        <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>css/slick-ltr.css">
    <?php endif; ?>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-ENHSH4H0XT"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-ENHSH4H0XT');
    </script>

</head>

<body>
    <div class="preloader">
        <div class="preloader-content">
            <img src="<?php echo $logo; ?>" alt="DIYAR KARAM MOTORS Logo" class="preloader-logo">
            <div class="loading-spinner"></div>
        </div>
    </div>
    <!-- Header -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top">
            <div class="container">
                <a class="navbar-brand" href="<?php echo createLink('index.php'); ?>" data-aos="fade-right" data-aos-duration="1000">
                    <img src="<?php echo $logo; ?>" alt="<?php echo ($currentLang == 'ar') ? SITE_NAME_AR : SITE_NAME_EN; ?>" height="50" class="logo-animation">
                </a>

                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php
                        $menuItems = [
                            'menu_home' => 'index.php',
                            'menu_cars' => 'cars.php',
                            'menu_services' => '#services',
                            'menu_about' => 'about-us.php',
                            'menu_branches' => 'our_branches.php',
                            'menu_contact' => 'contact.php'
                        ];

                        $delay = 100;
                        foreach ($menuItems as $key => $link):
                            $isActive = (strpos($_SERVER['PHP_SELF'], $link) !== false) ||
                                (basename($_SERVER['PHP_SELF']) == 'index.php' && $link == 'index.php');
                            $isHash = (strpos($link, '#') === 0);
                            $url = $isHash ? $link : createLink($link);
                        ?>
                            <li class="nav-item" data-aos="fade-down" data-aos-delay="<?php echo $delay; ?>">
                                <a class="nav-link <?php echo $isActive ? 'active' : ''; ?>"
                                    <?php echo $isActive ? 'aria-current="page"' : ''; ?>
                                    href="<?php echo $key == 'menu_services' ? createLink('index.php') . '#services' : $url; ?>">
                                    <?php echo $lang[$key]; ?>
                                </a>
                            </li>
                        <?php
                            $delay += 100;
                        endforeach;
                        ?>
                    </ul>

                    <div class="d-flex" data-aos="fade-left" data-aos-duration="1000" style="justify-content: center;">
                        <a href="?lang=<?php echo $oppositeLang; ?>" class="lang-switch">
                            <i class="fas fa-globe me-2"></i> <?php echo $oppositeLanguageName; ?>
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <?php if (isset($_SESSION['contact_success'])): ?>
            <div class="alert-box alert-success-custom animate__animated animate__fadeInDown">
                <button type="button" class="close-btn"><i class="fas fa-times"></i></button>
                <div class="d-flex align-items-center">
                    <div class="alert-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="alert-message"><?php echo $_SESSION['contact_success']; ?></div>
                </div>
            </div>
        <?php unset($_SESSION['contact_success']);
        endif; ?>

        <?php if (isset($_SESSION['contact_error'])): ?>
            <div class="alert-box alert-danger-custom animate__animated animate__fadeInDown">
                <button type="button" class="close-btn"><i class="fas fa-times"></i></button>
                <div class="d-flex align-items-center">
                    <div class="alert-icon"><i class="fas fa-exclamation-circle"></i></div>
                    <div class="alert-message"><?php echo $_SESSION['contact_error']; ?></div>
                </div>
            </div>
        <?php unset($_SESSION['contact_error']);
        endif; ?>
    </header>

    <main>