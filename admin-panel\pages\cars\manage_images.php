<?php

/**
 * صفحة إدارة صور السيارة
 * Car Images Management Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// عنوان الصفحة
$pageTitle = 'إدارة الصور';

$error = '';
$success = '';
$car = null;

// التحقق من وجود ID السيارة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$carId = intval($_GET['id']);

// استرجاع بيانات السيارة
try {
    $car = $db->Fetch("SELECT * FROM cars WHERE car_id = :car_id", ['car_id' => $carId]);
    if (!$car) {
        header('Location: index.php');
        exit;
    }
} catch (Exception $e) {
    $error = 'خطأ في استرجاع بيانات السيارة: ' . $e->getMessage();
}

// معالجة الطلبات AJAX
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    header('Content-Type: application/json');

    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'upload_image':
            if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
                echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
                exit;
            }

            if (isset($_FILES['image']) && !empty($_FILES['image']['name'])) {
                $uploadDir = UPLOAD_PATH . 'cars/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                $uploadResult = uploadFile($_FILES['image'], $uploadDir);
                if ($uploadResult['success']) {
                    $isMain = $_POST['is_main'] ?? 0;

                    // إذا كانت صورة رئيسية، تحديث الصور القديمة
                    if ($isMain) {
                        $db->Update("UPDATE car_images SET is_main = 0 WHERE car_id = :car_id", ['car_id' => $carId]);
                    }

                    // إدراج الصورة الجديدة
                    $imageStmt = "INSERT INTO car_images (car_id, image_url, is_main) VALUES (:car_id, :image_url, :is_main)";
                    $imageId = $db->Insert($imageStmt, [
                        'car_id' => $carId,
                        'image_url' => 'cars/' . $uploadResult['filename'],
                        'is_main' => $isMain
                    ]);

                    echo json_encode([
                        'success' => true,
                        'message' => 'تم رفع الصورة بنجاح',
                        'image_id' => $imageId,
                        'image_url' => UPLOAD_URL . 'cars/' . $uploadResult['filename']
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => $uploadResult['message']]);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'لم يتم اختيار صورة']);
            }
            exit;

        case 'delete_image':
            if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
                echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
                exit;
            }

            $imageId = intval($_POST['image_id'] ?? 0);
            if ($imageId > 0) {
                // استرجاع معلومات الصورة
                $image = $db->Fetch("SELECT * FROM car_images WHERE image_id = :image_id AND car_id = :car_id", [
                    'image_id' => $imageId,
                    'car_id' => $carId
                ]);

                if ($image) {
                    // حذف الملف الفعلي
                    $imagePath = UPLOAD_PATH . $image['image_url'];
                    if (file_exists($imagePath)) {
                        unlink($imagePath);
                    }

                    // حذف من قاعدة البيانات
                    $db->Remove("DELETE FROM car_images WHERE image_id = :image_id", ['image_id' => $imageId]);

                    // التحقق من نجاح الحذف
                    $imageExists = $db->Fetch("SELECT image_id FROM car_images WHERE image_id = :image_id", ['image_id' => $imageId]);

                    if (!$imageExists) {
                        // إذا كانت الصورة المحذوفة رئيسية، جعل أول صورة أخرى رئيسية
                        if ($image['is_main'] == 1) {
                            $firstImage = $db->Fetch(
                                "SELECT image_id FROM car_images WHERE car_id = :car_id ORDER BY image_id ASC LIMIT 1",
                                ['car_id' => $carId]
                            );

                            if ($firstImage) {
                                $db->Update(
                                    "UPDATE car_images SET is_main = 1 WHERE image_id = :image_id",
                                    ['image_id' => $firstImage['image_id']]
                                );
                            }
                        }

                        echo json_encode(['success' => true, 'message' => 'تم حذف الصورة بنجاح']);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'فشل في حذف الصورة من قاعدة البيانات']);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => 'الصورة غير موجودة']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'معرف الصورة غير صحيح']);
            }
            exit;

        case 'set_main':
            if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
                echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
                exit;
            }

            $imageId = intval($_POST['image_id'] ?? 0);
            if ($imageId > 0) {
                // التحقق من أن الصورة تخص هذه السيارة
                $image = $db->Fetch("SELECT * FROM car_images WHERE image_id = :image_id AND car_id = :car_id", [
                    'image_id' => $imageId,
                    'car_id' => $carId
                ]);

                if ($image) {
                    // إزالة الرئيسية من جميع الصور
                    $db->Update("UPDATE car_images SET is_main = 0 WHERE car_id = :car_id", ['car_id' => $carId]);

                    // تعيين الصورة كرئيسية
                    $db->Update("UPDATE car_images SET is_main = 1 WHERE image_id = :image_id", ['image_id' => $imageId]);

                    echo json_encode(['success' => true, 'message' => 'تم تعيين الصورة كرئيسية']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'الصورة غير موجودة']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'معرف الصورة غير صحيح']);
            }
            exit;

        default:
            echo json_encode(['success' => false, 'message' => 'عملية غير مدعومة']);
            exit;
    }
}

// استرجاع صور السيارة
$images = [];
if ($car) {
    $images = $db->FetchAll("SELECT * FROM car_images WHERE car_id = :car_id ORDER BY is_main DESC, image_id ASC", ['car_id' => $carId]);
}

include '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة صور السيارة</h1>
            <p class="text-muted">
                <?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar'] . ' - ' . $car['year']); ?>
            </p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $carId; ?>" class="btn btn-outline-primary">
                <i class="fas fa-eye me-2"></i>
                عرض السيارة
            </a>
            <a href="edit.php?id=<?php echo $carId; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-edit me-2"></i>
                تعديل السيارة
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <div id="alertContainer"></div>

    <div class="row">
        <!-- قسم رفع الصور -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>
                        رفع صورة جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <?php echo getCSRFTokenField(); ?>
                        <input type="hidden" name="action" value="upload_image">

                        <div class="mb-3">
                            <label class="form-label">اختر الصورة</label>
                            <input type="file"
                                class="form-control"
                                name="image"
                                id="imageInput"
                                accept="image/*,.webp,.avif"
                                required>
                            <small class="form-text text-muted">
                                الصيغ المدعومة: JPG, PNG, GIF, WebP, AVIF
                            </small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input"
                                    type="checkbox"
                                    name="is_main"
                                    id="isMain"
                                    value="1">
                                <label class="form-check-label" for="isMain">
                                    تعيين كصورة رئيسية
                                </label>
                            </div>
                        </div>

                        <!-- معاينة الصورة -->
                        <div id="imagePreview" class="mb-3 text-center" style="display: none;">
                            <img id="previewImg" src="" class="img-thumbnail" style="max-width: 200px;">
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-upload me-2"></i>
                            رفع الصورة
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- قسم إدارة الصور -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>
                        صور السيارة (<?php echo count($images); ?>)
                    </h5>
                    <?php if (count($images) > 0): ?>
                        <small class="text-muted">اسحب وأفلت لإعادة ترتيب الصور</small>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (count($images) > 0): ?>
                        <div class="row" id="imagesContainer">
                            <?php foreach ($images as $image): ?>
                                <div class="col-md-6 col-lg-4 mb-3" data-image-id="<?php echo $image['image_id']; ?>">
                                    <div class="card image-card">
                                        <div class="position-relative">
                                            <img src="<?php echo UPLOAD_PATH . $image['image_url']; ?>"
                                                class="card-img-top"
                                                style="height: 200px; object-fit: cover;"
                                                alt="صورة السيارة">

                                            <!-- شارة الصورة الرئيسية -->
                                            <?php if ($image['is_main']): ?>
                                                <span class="badge bg-primary position-absolute top-0 start-0 m-2">
                                                    <i class="fas fa-star me-1"></i>
                                                    رئيسية
                                                </span>
                                            <?php endif; ?>

                                            <!-- أزرار التحكم -->
                                            <div class="position-absolute top-0 end-0 m-2">
                                                <div class="btn-group-vertical" role="group">
                                                    <?php if (!$image['is_main']): ?>
                                                        <button type="button"
                                                            class="btn btn-sm btn-success btn-set-main"
                                                            data-image-id="<?php echo $image['image_id']; ?>"
                                                            title="تعيين كرئيسية">
                                                            <i class="fas fa-star"></i>
                                                        </button>
                                                    <?php endif; ?>

                                                    <button type="button"
                                                        class="btn btn-sm btn-danger btn-delete"
                                                        data-image-id="<?php echo $image['image_id']; ?>"
                                                        title="حذف الصورة">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card-body p-2">
                                            <small class="text-muted">
                                                رقم الصورة: <?php echo $image['image_id']; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد صور لهذه السيارة</h5>
                            <p class="text-muted">ابدأ برفع الصور باستخدام النموذج على اليسار</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذه الصورة؟</p>
                <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo getCSRFTokenField(); ?>
                    <input type="hidden" name="action" value="delete_image">
                    <input type="hidden" name="image_id" id="deleteImageId" value="">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>حذف الصورة
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    let currentImageId = null;

    // معاينة الصورة قبل الرفع
    document.getElementById('imageInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });

    // رفع الصورة
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جارٍ الرفع...';

        fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    // إعادة تحميل الصفحة لعرض الصورة الجديدة
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ أثناء رفع الصورة');
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
    });

    // تعيين كصورة رئيسية
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-set-main')) {
            const imageId = e.target.closest('.btn-set-main').dataset.imageId;
            setMainImage(imageId);
        }
    });

    // حذف الصورة
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn-delete')) {
            currentImageId = e.target.closest('.btn-delete').dataset.imageId;
            // تعيين معرف الصورة في النموذج
            document.getElementById('deleteImageId').value = currentImageId;
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
    });

    // معالجة نموذج الحذف
    document.getElementById('deleteForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جارٍ الحذف...';
        
        fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    // إزالة الصورة من الواجهة
                    const imageCard = document.querySelector(`[data-image-id="${currentImageId}"]`);
                    if (imageCard) {
                        imageCard.remove();
                    }
                    // تحديث عدد الصور المعروض
                    const imageCount = document.querySelector('.card-title .text-muted');
                    if (imageCount) {
                        const currentCount = parseInt(imageCount.textContent.match(/\d+/)[0]);
                        imageCount.textContent = `صور السيارة (${currentCount - 1})`;
                    }
                    // إخفاء النافذة
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    deleteModal.hide();
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ أثناء حذف الصورة');
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
    });

    // دالة تعيين الصورة الرئيسية
    function setMainImage(imageId) {
        const formData = new FormData();
        formData.append('action', 'set_main');
        formData.append('image_id', imageId);
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

        fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ أثناء تعيين الصورة الرئيسية');
                console.error('Error:', error);
            });
    }

    // دالة حذف الصورة
    function deleteImage(imageId) {
        const formData = new FormData();
        formData.append('action', 'delete_image');
        formData.append('image_id', imageId);
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

        fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // showAlert('success', data.message);
                    // إزالة الصورة من الواجهة
                    const imageCard = document.querySelector(`[data-image-id="${imageId}"]`);
                    if (imageCard) {
                        imageCard.remove();
                    }
                } else {
                    showAlert('danger', data.message);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ أثناء حذف الصورة');
                console.error('Error:', error);
            });
    }

    // دالة عرض الرسائل
    function showAlert(type, message) {
        const alertContainer = document.getElementById('alertContainer');
        const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
        alertContainer.innerHTML = alertHtml;

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
</script>

<?php include '../../includes/footer.php'; ?>