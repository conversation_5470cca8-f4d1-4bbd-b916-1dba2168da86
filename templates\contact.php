<?php
$currentLang = getCurrentLanguage();
?>

<!-- Contact Hero Section -->
<section class="hero-section position-relative text-center" style="padding-top: 100px;">

    <div class="animated-bg" data-url="<?php echo ASSETS_PATH; ?>" style=" background-image: url('<?php echo ASSETS_PATH; ?>images/k4-25my-kv-pc.jpg');"></div>
    <div class="container" style="z-index: 2;">
        <div class="row min-vh-80 align-items-center">
            <div class="text-white flex flex-col gap-5 pb-5 w-full position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <h1 class="display-4 fw-bold mb-3 text-white animate__animated animate__fadeInDown" style="text-transform: uppercase; letter-spacing: 1.5px;"><?php echo $lang['contact_title']; ?></h1>
                <div class="divider mx-auto mb-4" style="height: 4px; width: 70px; background-color: #05141f;"></div>
                <div class="text-rotator">
                    <p class="lead mb-4 text-white rotating-text active"><?php echo $lang['hero_subtitle1']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle2']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle3']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle4']; ?></p>
                </div>
            </div>
        </div>
    </div>
    <svg data-name="VIDEO GRADIENT" xmlns="http://www.w3.org/2000/svg" class="home-hero__gradient" role="presentation">
        <defs data-v-1bfe21c6="">
            <linearGradient id="DesktopGradient_svg__a" x1=".5" y1=".129" x2=".5" y2=".708" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-opacity="0"></stop>
                <stop offset=".364" stop-opacity=".424"></stop>
                <stop offset=".64" stop-color="#030b11" stop-opacity=".733"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
            <linearGradient id="DesktopGradient_svg__b" x1=".5" y1=".129" x2=".5" y2=".5" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-color="#05141f" stop-opacity="0"></stop>
                <stop offset=".226" stop-color="#05141f" stop-opacity=".082"></stop>
                <stop offset=".512" stop-color="#05141f" stop-opacity=".271"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
        </defs>
        <rect fill="url(#DesktopGradient_svg__a)" width="100%" height="204"></rect>
        <rect fill="url(#DesktopGradient_svg__b)" y="72" width="100%" height="132"></rect>
    </svg>
</section>

<!-- Contact Form & Info Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-12" data-aos="fade-up">
                <div class="contact-form bg-light p-4 p-md-5 rounded shadow-sm">
                    <h3 class="mb-4 border-bottom pb-3"><?php echo $lang['contact_title']; ?></h3>
                    <form id="contactForm" action="process_contact.php" method="post">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="name" name="name" placeholder="<?php echo $lang['contact_name']; ?>" required>
                                    <label for="name"><?php echo $lang['contact_name']; ?></label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" placeholder="<?php echo $lang['contact_email']; ?>" required>
                                    <label for="email"><?php echo $lang['contact_email']; ?></label>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="phone" name="phone" placeholder="<?php echo $lang['contact_phone']; ?>" required>
                                    <label for="phone"><?php echo $lang['contact_phone']; ?></label>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="message" name="message" placeholder="<?php echo $lang['contact_message']; ?>" style="height: 150px" required></textarea>
                                    <label for="message"><?php echo $lang['contact_message']; ?></label>
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <?php echo $lang['contact_send']; ?>
                                    <i class="fas fa-paper-plane ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const texts = document.querySelectorAll('.rotating-text');
        let currentIndex = 0;
        const intervalTime = 10000;

        function rotateText() {
            const currentActive = document.querySelector('.rotating-text.active');
            if (currentActive) {
                currentActive.classList.add('fade-out');
                currentActive.classList.remove('active');
            }

            currentIndex = (currentIndex + 1) % texts.length;

            setTimeout(() => {
                texts[currentIndex].classList.remove('fade-out');
                texts[currentIndex].classList.add('active');
            }, 300);
        }

        let rotationInterval = setInterval(rotateText, intervalTime);

    });
</script>