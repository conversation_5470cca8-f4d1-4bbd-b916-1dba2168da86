<?php
session_start();

include_once '../includes/config.php';
include_once '../includes/functions.php';

$lang = getCurrentLanguage();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $car_id = isset($_POST['car_id']) && is_numeric($_POST['car_id']) ? intval($_POST['car_id']) : 0;
    
    if ($car_id > 0) {
        if ($action === 'add') {
            $result = addToComparison($car_id);
            if ($result) {
                $_SESSION['comparison_success'] = ($lang == 'ar') ? 
                    'تم إضافة السيارة للمقارنة بنجاح!' : 
                    'Car added to comparison successfully!';
            } else {
                $_SESSION['comparison_error'] = ($lang == 'ar') ? 
                    'السيارة موجودة بالفعل في المقارنة!' : 
                    'Car is already in comparison!';
            }
        } elseif ($action === 'remove') {
            removeFromComparison($car_id);
            $_SESSION['comparison_success'] = ($lang == 'ar') ? 
                'تم إزالة السيارة من المقارنة!' : 
                'Car removed from comparison!';
        }
    }
}

// Return JSON response for AJAX requests
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    header('Content-Type: application/json');
    $comparisonCars = getComparisonCars();
    echo json_encode([
        'success' => true,
        'count' => count($comparisonCars),
        'message' => $_SESSION['comparison_success'] ?? $_SESSION['comparison_error'] ?? ''
    ]);
    unset($_SESSION['comparison_success'], $_SESSION['comparison_error']);
    exit();
}

// For regular requests, redirect back
$referer = $_SERVER['HTTP_REFERER'] ?? createLink('cars.php');
header('Location: ' . $referer);
exit();
?>