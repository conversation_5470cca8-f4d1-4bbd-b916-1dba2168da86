/* Slick Carousel */
/* Background Section Specific Styles */
.carousel-background-section {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-image: url('https://diyar-alkaram.com.iq/assets/images/kia_homepage_meet-the-family_background_suv-cuv-mpv-v8_XL.jpeg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.carousel-background-section .background-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.3)
  ); /* More subtle overlay */
  z-index: 1;
}

/* Custom Navigation Arrows (now part of background section) */
.carousel-background-section .custom-prev-arrow,
.carousel-background-section .custom-next-arrow {
  top: 50%; /* Adjust as needed, possibly slightly lower than 50% */
  transform: translateY(100px); /* Move down from center */
  z-index: 20;
  width: 3.5rem; /* Slightly larger */
  height: 3.5rem; /* Slightly larger */
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%; /* Ensure circular shape */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.carousel-background-section .custom-prev-arrow {
  right: 6rem; /* Adjust position for RTL */
}

.carousel-background-section .custom-next-arrow {
  left: 6rem; /* Adjust position for RTL */
}

.carousel-background-section .custom-prev-arrow:hover,
.carousel-background-section .custom-next-arrow:hover {
  background: white;
  transform: translateY(100px) scale(1.1);
}

.carousel-background-section .custom-prev-arrow i,
.carousel-background-section .custom-next-arrow i {
  color: #1a1a1a;
  font-size: 1.5rem; /* Slightly larger icon */
}

/* Carousel Styles (now outside background section) */
.kia-carousel {
  position: relative;
  z-index: 10;
  padding-top: 0rem; /* Further reduced padding-top */
  margin-top: -40vh; /* Adjusted margin-top to bring it higher */
}
.main-container .car-info-slick {
  color: black;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5); /* Add text shadow for better readability */
}
.main-container .slide {
  outline: none;
  padding: 0 0.5rem; /* Further reduced padding */
}

.main-container .slide-content {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  padding-bottom: 2rem; /* Add some padding at the bottom of the slide content */
}

.main-container .car-image {
  position: relative;
  margin-bottom: 1rem; /* Reduced margin-bottom */
}

.main-container .car-image img {
  width: 100%;
  max-width: 50rem; /* Further reduced max-width for images */
  height: auto;
  object-fit: contain;
  filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.5));
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
/* .slick-current .car-image img {
  width: 100%;
} */
.main-container .car-year-slick {
  letter-spacing: 0.05em;
  font-size: 1rem; /* Further adjusted font size */
  margin-bottom: 0.5rem; /* Reduced margin-bottom */
}

.main-container .car-model-slick {
  letter-spacing: -0.025em;
  line-height: 1;
  font-size: 3rem; /* Further adjusted font size */
  margin-bottom: 1rem; /* Reduced margin-bottom */
}

.main-container .spec-label {
  font-weight: 500;
  letter-spacing: 0.05em;
  font-size: 0.7rem; /* Further adjusted font size */
  margin-bottom: 0.2rem; /* Reduced margin-bottom */
}

.main-container .spec-value {
  font-size: 1.5rem; /* Further adjusted font size */
}

.main-container .car-specs-slick {
  margin-bottom: 1.5rem; /* Reduced margin-bottom */
}

.main-container .action-buttons .btn {
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-size: 0.8rem; /* Further adjusted font size */
  padding: 0.5rem 1.2rem; /* Further adjusted padding */
}

.main-container .action-buttons .btn-outline-light:hover {
  transform: scale(1.05);
}

.main-container .action-buttons .btn-dark:hover {
  transform: scale(1.05);
}

/* Slick Carousel Customization */
.kia-carousel .slick-slide {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  filter: blur(2px);
  opacity: 0.4;
  transform: scale(0.85);
}

.kia-carousel .slick-slide.slick-center {
  filter: blur(0px);
  opacity: 1;
  transform: scale(1);
  z-index: 10;
}

.kia-carousel .slick-track {
  display: flex;
  align-items: center;
}

.kia-carousel .slick-slide > div {
  height: 100%;
}

/* Hide default slick arrows */
.kia-carousel .slick-prev,
.kia-carousel .slick-next {
  display: none !important;
}

/* Enhanced visual effects */
.kia-carousel .slick-slide img {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.kia-carousel .slick-center img {
  filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.5));
}

/* Smooth text animations */
.kia-carousel .slick-slide .car-info {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.kia-carousel .slick-center .car-info {
  transform: translateY(0);
}

.kia-carousel .slick-slide:not(.slick-center) .car-info {
  transform: translateY(20px);
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .carousel-background-section .custom-prev-arrow {
    right: 2rem;
  }

  .carousel-background-section .custom-next-arrow {
    left: 2rem;
  }
}

@media (max-width: 768px) {
  .carousel-background-section .custom-prev-arrow {
    right: 1rem;
  }

  .carousel-background-section .custom-next-arrow {
    left: 1rem;
  }
}

@media (max-width: 480px) {
  .carousel-background-section .custom-prev-arrow,
  .carousel-background-section .custom-next-arrow {
    width: 2.5rem;
    height: 2.5rem;
  }

  .carousel-background-section .custom-prev-arrow i,
  .carousel-background-section .custom-next-arrow i {
    font-size: 1rem;
  }
}
