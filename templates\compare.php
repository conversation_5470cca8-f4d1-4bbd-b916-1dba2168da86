<?php
$currentLang = getCurrentLanguage();

// Get comparison cars images for background
$backgroundImages = [];
if (!empty($comparisonCars)) {
    foreach ($comparisonCars as $car) {
        if (!empty($car['main_image'])) {
            $backgroundImages[] = UPLOADS_PATH . htmlspecialchars($car['main_image']);
        }
    }
}

// Create background style
$backgroundStyle = '';
if (!empty($backgroundImages)) {
    if (count($backgroundImages) == 1) {
        // Single car image
        $backgroundStyle = "background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('{$backgroundImages[0]}'); background-size: cover; background-position: center;";
    } else {
        // Multiple car images - create a collage effect
        $gradientStops = [];
        $imageCount = count($backgroundImages);
        for ($i = 0; $i < min($imageCount, 4); $i++) {
            $percentage = ($i / 4) * 100;
            $nextPercentage = (($i + 1) / 4) * 100;
            $gradientStops[] = "url('{$backgroundImages[$i]}') {$percentage}% 0 / " . (100 / min($imageCount, 4)) . "% 100% no-repeat";
        }
        $backgroundStyle = "background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), " . implode(', ', $gradientStops) . "; background-size: cover;";
    }
} else {
    // Fallback to primary color
    $backgroundStyle = "background-color: var(--primary-color);";
}
?>

<!-- Compare Hero Section -->
<section class="page-hero text-white py-5" style="<?php echo $backgroundStyle; ?> padding-top: 120px; background-attachment: fixed;">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-balance-scale me-3"></i>
                    <?php echo ($currentLang == 'ar') ? 'مقارنة السيارات' : 'Car Comparison'; ?>
                </h1>
                <p class="lead">
                    <?php echo ($currentLang == 'ar') ? 'قارن بين السيارات المختلفة لاتخاذ القرار الأمثل' : 'Compare different cars to make the best decision'; ?>
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Comparison Section -->
<section class="py-5 bg-light">
    <div class="container">
        <?php if (empty($comparisonCars)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="card shadow-sm">
                        <div class="card-body py-5">
                            <i class="fas fa-balance-scale fa-4x text-muted mb-4"></i>
                            <h3 class="text-muted mb-3">
                                <?php echo ($currentLang == 'ar') ? 'لا توجد سيارات للمقارنة' : 'No cars to compare'; ?>
                            </h3>
                            <p class="text-muted mb-4">
                                <?php echo ($currentLang == 'ar') ? 'أضف سيارات من صفحة السيارات لبدء المقارنة' : 'Add cars from the cars page to start comparing'; ?>
                            </p>
                            <a href="<?php echo createLink('cars.php'); ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-car me-2"></i>
                                <?php echo ($currentLang == 'ar') ? 'تصفح السيارات' : 'Browse Cars'; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3>
                            <?php echo ($currentLang == 'ar') ? 'مقارنة ' . count($comparisonCars) . ' سيارات' : 'Comparing ' . count($comparisonCars) . ' cars'; ?>
                        </h3>
                        <a href="<?php echo createLink('cars.php'); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>
                            <?php echo ($currentLang == 'ar') ? 'إضافة المزيد' : 'Add More'; ?>
                        </a>
                    </div>
                </div>
            </div>

            <div class="comparison-table-wrapper">
                <div class="table-responsive">
                    <table class="table table-bordered bg-white shadow-sm">
                        <thead class="text-white" style="background-color: var(--primary-color);">
                            <tr>
                                <th style="width: 200px;">
                                    <?php echo ($currentLang == 'ar') ? 'المواصفات' : 'Specifications'; ?>
                                </th>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <th class="text-center" style="min-width: 250px;">
                                        <div class="position-relative">
                                            <button class="btn btn-sm btn-outline-light position-absolute top-0 end-0 remove-from-comparison"
                                                data-car-id="<?php echo $car['car_id']; ?>"
                                                title="<?php echo ($currentLang == 'ar') ? 'إزالة من المقارنة' : 'Remove from comparison'; ?>">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <img src="<?php echo UPLOADS_PATH . ($car['main_image'] ?? 'default.jpg'); ?>"
                                                alt="<?php echo ($currentLang == 'ar') ? $car['brand_ar'] . ' ' . $car['model_ar'] : $car['brand_en'] . ' ' . $car['model_en']; ?>"
                                                class="img-fluid rounded mb-2" style="height: 120px; object-fit: cover;">
                                            <h6 class="mb-1">
                                                <?php echo ($currentLang == 'ar') ? $car['brand_ar'] . ' ' . $car['model_ar'] : $car['brand_en'] . ' ' . $car['model_en']; ?>
                                            </h6>
                                            <small class="text-light">
                                                <?php echo $car['year']; ?>
                                            </small>
                                        </div>
                                    </th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Price Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-dollar-sign text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'السعر' : 'Price'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <span class="h5 text-primary fw-bold">
                                            $<?php echo number_format($car['price'] ?? 0); ?>
                                        </span>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Brand Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-car text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'العلامة التجارية' : 'Brand'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo ($currentLang == 'ar') ? $car['brand_ar'] : $car['brand_en']; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Model Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-tag text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'الموديل' : 'Model'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo ($currentLang == 'ar') ? $car['model_ar'] : $car['model_en']; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Year Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-calendar text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'سنة الصنع' : 'Year'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo $car['year'] ?? '--'; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Fuel Type Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-gas-pump text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'نوع الوقود' : 'Fuel Type'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo ($currentLang == 'ar') ? ($car['fuel_type_ar'] ?? '--') : ($car['fuel_type_en'] ?? '--'); ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Transmission Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-cog text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'ناقل الحركة' : 'Transmission'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo ($currentLang == 'ar') ? ($car['transmission_ar'] ?? '--') : ($car['transmission_en'] ?? '--'); ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Engine Size Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-oil-can text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'حجم المحرك' : 'Engine Size'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo $car['engine_size'] ?? '--'; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Doors Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-door-open text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'عدد الأبواب' : 'Doors'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo $car['doors'] ?? '--'; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Seats Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-users text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'عدد المقاعد' : 'Seats'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo $car['seats'] ?? '--'; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Mileage Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-road text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'عدد الكيلومترات' : 'Mileage'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo number_format($car['mileage'] ?? 0); ?> km
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Color Row -->
                            <tr>
                                <td class="fw-bold bg-light">
                                    <i class="fas fa-palette text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'اللون' : 'Color'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <?php echo ($currentLang == 'ar') ? ($car['color_ar'] ?? '--') : ($car['color_en'] ?? '--'); ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Actions Row -->
                            <tr class="bg-light">
                                <td class="fw-bold">
                                    <i class="fas fa-tools text-primary me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'الإجراءات' : 'Actions'; ?>
                                </td>
                                <?php foreach ($comparisonCars as $car): ?>
                                    <td class="text-center">
                                        <div class="btn-group-vertical gap-2">
                                            <a href="<?php echo createLink('car_details.php', true, $car['car_id']); ?>"
                                                class="btn btn-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>
                                                <?php echo ($currentLang == 'ar') ? 'عرض التفاصيل' : 'View Details'; ?>
                                            </a>
                                            <a href="<?php echo createLink('contact.php'); ?>"
                                                class="btn btn-success btn-sm">
                                                <i class="fas fa-phone me-1"></i>
                                                <?php echo ($currentLang == 'ar') ? 'اتصل بنا' : 'Contact Us'; ?>
                                            </a>
                                        </div>
                                    </td>
                                <?php endforeach; ?>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12 text-center">
                    <a href="<?php echo createLink('cars.php'); ?>" class="btn btn-outline-primary btn-lg me-3">
                        <i class="fas fa-arrow-left me-2"></i>
                        <?php echo ($currentLang == 'ar') ? 'العودة للسيارات' : 'Back to Cars'; ?>
                    </a>
                    <button class="btn btn-danger btn-lg" onclick="clearAllComparisons()">
                        <i class="fas fa-trash me-2"></i>
                        <?php echo ($currentLang == 'ar') ? 'مسح جميع المقارنات' : 'Clear All Comparisons'; ?>
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<script>
    // Remove single car from comparison
    document.querySelectorAll('.remove-from-comparison').forEach(button => {
        button.addEventListener('click', function() {
            const carId = this.dataset.carId;

            fetch('process_comparison.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `action=remove&car_id=${carId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });
    });

    // Clear all comparisons
    function clearAllComparisons() {
        if (confirm('<?php echo ($currentLang == 'ar') ? 'هل أنت متأكد من مسح جميع المقارنات؟' : 'Are you sure you want to clear all comparisons?'; ?>')) {
            <?php foreach ($comparisonCars as $car): ?>
                fetch('process_comparison.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `action=remove&car_id=<?php echo $car['car_id']; ?>`
                });
            <?php endforeach; ?>

            setTimeout(() => {
                location.reload();
            }, 500);
        }
    }
</script>