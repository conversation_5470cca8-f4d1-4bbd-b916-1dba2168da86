# =============================================================================
# SECURITY PROTECTION - حماية الملفات المهمة
# =============================================================================

# منع الوصول للملفات الحساسة - Block access to sensitive files
<FilesMatch "\.(sql|log|md|txt|json|xml|yml|yaml|ini|conf|bak|backup|old|tmp|temp)$">
    Require all denied
</FilesMatch>

# منع الوصول لملفات التكوين - Block configuration files
<FilesMatch "^(config|database|\.env|\.htaccess|\.htpasswd|composer\.(json|lock)|package\.(json|lock))$">
    Require all denied
</FilesMatch>

# منع الوصول لمجلدات النظام - Block system directories
RedirectMatch 403 ^/includes/.*$
RedirectMatch 403 ^/admin-panel/includes/.*$
RedirectMatch 403 ^/cron/.*$
RedirectMatch 403 ^/logs/.*$
RedirectMatch 403 ^/backups/.*$
RedirectMatch 403 ^/vendor/.*$
RedirectMatch 403 ^/node_modules/.*$
RedirectMatch 403 ^/\.git/.*$

# منع الوصول لملفات PHP الحساسة - Block sensitive PHP files
<FilesMatch "^(analytics_functions|track_visit|config|database)\.php$">
    <RequireAll>
        Require local
        Require ip 127.0.0.1
        Require ip ::1
    </RequireAll>
</FilesMatch>

# حماية مجلد admin-panel - Protect admin panel
<Directory "admin-panel">
    <FilesMatch "\.(php|css|js|png|jpg|jpeg|gif|ico|svg)$">
        Require all granted
    </FilesMatch>
    <FilesMatch "^(?!.*\.(php|css|js|png|jpg|jpeg|gif|ico|svg)$).*$">
        Require all denied
    </FilesMatch>
</Directory>

# منع تنفيذ PHP في مجلدات الرفع - Prevent PHP execution in upload directories
<Directory "uploads">
    <FilesMatch "\.php$">
        Require all denied
    </FilesMatch>
    php_flag engine off
</Directory>

# منع الوصول للملفات المخفية - Block hidden files
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# =============================================================================
# URL REWRITING & SECURITY - إعادة كتابة الروابط والحماية
# =============================================================================

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # منع محاولات الوصول للملفات الحساسة - Block attempts to access sensitive files
    RewriteRule ^(includes|admin-panel/includes|cron|logs|backups)/ - [F,L]
    
    # منع الاستعلامات الخبيثة الخطيرة فقط - Block only dangerous malicious queries
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} ^.*(globals|localhost|loopback).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(select|insert|union|declare|drop|delete|update).*from [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(<script|javascript:|vbscript:|onload=|onerror=).* [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # إعادة توجيه النطاق - Domain redirect
    RewriteCond %{HTTP_HOST} ^diyar-alkaram\.com\.iq [NC]
    RewriteRule ^ https://www.diyar-alkaram.com.iq%{REQUEST_URI} [L,R=301]
    
    # فرض HTTPS - Force HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # إعادة كتابة URL - URL rewriting
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.html [L]
</IfModule>

# =============================================================================
# GENERAL CONFIGURATION - الإعدادات العامة
# =============================================================================

# منع عرض محتويات المجلدات - Disable directory browsing
Options -Indexes -MultiViews

# ضغط الملفات - File compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json
</IfModule>

# تخزين مؤقت للملفات - File caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/* "access plus 6 months"
</IfModule>

# =============================================================================
# SECURITY HEADERS - رؤوس الأمان
# =============================================================================

<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية - Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # منع تشغيل المحتوى الخبيث - Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # تفعيل حماية XSS - Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # سياسة الأمان للمحتوى - Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com https://maps.googleapis.com https://maps.gstatic.com https://*.googleapis.com https://*.gstatic.com https://code.jquery.com https://www.googletagmanager.com https://static.cloudflareinsights.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com https://maps.googleapis.com https://*.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src 'self' data: https: blob: https://maps.googleapis.com https://maps.gstatic.com https://*.googleapis.com https://*.gstatic.com https://*.ggpht.com; connect-src 'self' https://maps.googleapis.com https://*.googleapis.com https://www.google-analytics.com https://cloudflareinsights.com; frame-src 'self' https://www.google.com https://maps.google.com https://*.google.com; child-src 'self' https://www.google.com https://maps.google.com; worker-src 'self' blob:;"
    
    # إخفاء معلومات الخادم - Hide server information
    Header always unset Server
    Header always unset X-Powered-By
    
    # سياسة الإحالة - Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # حماية من هجمات التوقيت - Timing attack protection
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
</IfModule>

# =============================================================================
# FILE PROTECTION - حماية الملفات
# =============================================================================

# منع الوصول لملفات النسخ الاحتياطي - Block backup files
<FilesMatch "\.(bak|backup|swp|tmp|temp|~|#)$">
    Require all denied
</FilesMatch>

# منع الوصول للملفات الحساسة بالامتداد - Block sensitive file extensions
<FilesMatch "\.(inc|conf|cnf|cfg|ini|log|sql|bak|backup|old|orig|original|tmp|temp|swp|swo|~|#)$">
    Require all denied
</FilesMatch>

# حماية ملفات composer و npm - Protect composer and npm files
<FilesMatch "^(composer\.(json|lock)|package(-lock)?\.json|yarn\.lock)$">
    Require all denied
</FilesMatch>

# منع الوصول لملفات Git - Block Git files
<FilesMatch "^\.git">
    Require all denied
</FilesMatch>

# حد حجم الطلبات - Limit request size (10MB)
LimitRequestBody 10485760

# منع الوصول المباشر لملفات include - Block direct access to include files
<Directory "includes">
    <FilesMatch "\.php$">
        <RequireAll>
            Require local
            Require ip 127.0.0.1
            Require ip ::1
        </RequireAll>
    </FilesMatch>
</Directory>

# حماية مجلد cron - Protect cron directory
<Directory "cron">
    Require all denied
</Directory>

# حماية مجلد logs - Protect logs directory
<Directory "logs">
    Require all denied
</Directory>

# =============================================================================
# PERFORMANCE OPTIMIZATION - تحسين الأداء
# =============================================================================

# تفعيل ضغط gzip - Enable gzip compression
<IfModule mod_deflate.c>
    <IfModule mod_setenvif.c>
        <IfModule mod_headers.c>
            SetEnvIfNoCase ^(Accept-EncodXng|X-cept-Encoding|X{15}|~{15}|-{15})$ ^((gzip|deflate)\s*,?\s*)+|[X~-]{4,13}$ HAVE_Accept-Encoding
            RequestHeader append Accept-Encoding "gzip,deflate" env=HAVE_Accept-Encoding
        </IfModule>
    </IfModule>
    
    # ضغط أنواع الملفات المختلفة - Compress different file types
    AddOutputFilterByType DEFLATE application/atom+xml \
                                  application/javascript \
                                  application/json \
                                  application/rss+xml \
                                  application/vnd.ms-fontobject \
                                  application/x-font-ttf \
                                  application/x-web-app-manifest+json \
                                  application/xhtml+xml \
                                  application/xml \
                                  font/opentype \
                                  image/svg+xml \
                                  image/x-icon \
                                  text/css \
                                  text/html \
                                  text/plain \
                                  text/x-component \
                                  text/xml
</IfModule>

# تحسين التخزين المؤقت - Optimize caching
<IfModule mod_expires.c>
    ExpiresActive on
    
    # HTML
    ExpiresByType text/html                             "access plus 0 seconds"
    
    # CSS
    ExpiresByType text/css                              "access plus 1 month"
    
    # JavaScript
    ExpiresByType application/javascript                "access plus 1 month"
    ExpiresByType application/x-javascript              "access plus 1 month"
    ExpiresByType text/javascript                       "access plus 1 month"
    
    # Images
    ExpiresByType image/gif                             "access plus 1 month"
    ExpiresByType image/jpeg                            "access plus 1 month"
    ExpiresByType image/png                             "access plus 1 month"
    ExpiresByType image/svg+xml                         "access plus 1 month"
    ExpiresByType image/x-icon                          "access plus 1 year"
    
    # Fonts
    ExpiresByType application/vnd.ms-fontobject         "access plus 1 month"
    ExpiresByType font/truetype                         "access plus 1 month"
    ExpiresByType font/woff                             "access plus 1 month"
    ExpiresByType font/woff2                            "access plus 1 month"
    
    # Other
    ExpiresByType application/pdf                       "access plus 1 month"
    ExpiresByType application/x-shockwave-flash         "access plus 1 month"
</IfModule>

# =============================================================================
# ALLOW LEGITIMATE PARAMETERS - السماح بالمعاملات الشرعية
# =============================================================================

# السماح بمعاملات الموقع الشرعية - Allow legitimate site parameters
# lang, category, brand, id, page, search, filter, sort, limit, offset
# هذه المعاملات مسموحة ��لن تؤدي إلى 403