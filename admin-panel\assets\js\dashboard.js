/**
 * ملف JavaScript المخصص للوحة التحكم
 * Custom JavaScript for Admin Dashboard
 */

$(document).ready(function() {

    // تهيئة عامة
    initializeDashboard();

    // تفعيل Tooltips
    initializeTooltips();

    // تفعيل DataTables
    initializeDataTables();

    // تفعيل معاينة الصور
    initializeImagePreview();

    // تفعيل الحقول الديناميكية
    initializeDynamicFields();

    // تفعيل رفع الملفات بالسحب والإفلات
    initializeDragAndDrop();

    // تفعيل التحقق من النماذج
    initializeFormValidation();
});

/**
 * التهيئة العامة للوحة التحكم
 */
function initializeDashboard() {
    // تفعيل الشريط الجانبي
    $('#toggleSidebar').on('click', function() {
        $('#sidebar').toggleClass('collapsed');
        localStorage.setItem('sidebarCollapsed', $('#sidebar').hasClass('collapsed'));
    });

    // استعادة حالة الشريط الجانبي
    if (localStorage.getItem('sidebarCollapsed') === 'true') {
        $('#sidebar').addClass('collapsed');
    }

    // إخفاء الشريط الجانبي في الأجهزة المحمولة
    if ($(window).width() <= 768) {
        $('#sidebar').addClass('collapsed');
    }

    // معالجة تغيير حجم النافذة
    $(window).on('resize', function() {
        if ($(window).width() <= 768) {
            $('#sidebar').addClass('show');
        } else {
            $('#sidebar').removeClass('show');
        }
    });

    // إضافة تأثيرات الحركة للبطاقات
    $('.card').addClass('fade-in');

    // تفعيل البحث السريع
    if ($('#quickSearch').length) {
        $('#quickSearch').on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            $('.searchable').each(function() {
                var text = $(this).text().toLowerCase();
                $(this).closest('tr').toggle(text.indexOf(searchTerm) > -1);
            });
        });
    }
}

/**
 * تفعيل Tooltips
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            placement: 'top',
            trigger: 'hover'
        });
    });
}

/**
 * تفعيل DataTables
 */
function initializeDataTables() {
    if ($('.data-table').length) {
        $('.data-table').each(function() {
            var tableId = $(this).attr('id');
            var config = {
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
                },
                responsive: true,
                pageLength: 10,
                order: [[0, 'desc']],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success btn-sm'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger btn-sm'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> طباعة',
                        className: 'btn btn-info btn-sm'
                    }
                ]
            };

            // تخصيص إعدادات حسب نوع الجدول
            if (tableId === 'carsTable') {
                config.columnDefs = [
                    { orderable: false, targets: [0, -1] }, // إلغاء الترتيب للصورة والإجراءات
                    { className: 'text-center', targets: [0, 2, 5, 6] }
                ];
            }

            $(this).DataTable(config);
        });
    }
}

/**
 * تفعيل معاينة الصور
 */
function initializeImagePreview() {
    // معاينة الصور قبل الرفع
    $('.image-input').on('change', function(e) {
        var file = e.target.files[0];
        var previewId = $(this).data('preview');

        if (file && previewId) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#' + previewId).attr('src', e.target.result).show();
            };
            reader.readAsDataURL(file);
        }
    });

    // تكبير الصور عند النقر
    $('.img-thumbnail, .car-thumbnail').on('click', function() {
        var imgSrc = $(this).attr('src');
        var imgAlt = $(this).attr('alt') || 'صورة';

        var modal = $('<div class="modal fade" tabindex="-1">' +
            '<div class="modal-dialog modal-lg modal-dialog-centered">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<h5 class="modal-title">' + imgAlt + '</h5>' +
            '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
            '</div>' +
            '<div class="modal-body text-center">' +
            '<img src="' + imgSrc + '" class="img-fluid" alt="' + imgAlt + '">' +
            '</div>' +
            '</div>' +
            '</div>' +
            '</div>');

        $('body').append(modal);
        modal.modal('show');

        modal.on('hidden.bs.modal', function() {
            modal.remove();
        });
    });
}

/**
 * تفعيل الحقول الديناميكية
 */
function initializeDynamicFields() {
    // إضافة حقل جديد
    $(document).on('click', '.add-field', function(e) {
        e.preventDefault();
        var container = $(this).data('container');
        var template = $(this).data('template');

        if (container && template) {
            var fieldHtml = $('#' + template).html();
            $('#' + container).append(fieldHtml);
        }
    });

    // حذف حقل
    $(document).on('click', '.remove-field', function(e) {
        e.preventDefault();
        $(this).closest('.dynamic-field').remove();
    });

    // إضافة مميزة جديدة للسيارة
    $('#addFeature').on('click', function() {
        var featureHtml = '<div class="row dynamic-field mb-3">' +
            '<div class="col-md-3">' +
            '<input type="text" class="form-control" name="features[][type_ar]" placeholder="نوع الميزة (عربي)">' +
            '</div>' +
            '<div class="col-md-3">' +
            '<input type="text" class="form-control" name="features[][type_en]" placeholder="نوع الميزة (إنجليزي)">' +
            '</div>' +
            '<div class="col-md-2">' +
            '<input type="text" class="form-control" name="features[][name_ar]" placeholder="اسم الميزة (عربي)">' +
            '</div>' +
            '<div class="col-md-2">' +
            '<input type="text" class="form-control" name="features[][name_en]" placeholder="اسم الميزة (إنجليزي)">' +
            '</div>' +
            '<div class="col-md-1">' +
            '<input type="text" class="form-control" name="features[][value_ar]" placeholder="القيمة">' +
            '</div>' +
            '<div class="col-md-1">' +
            '<button type="button" class="btn btn-danger btn-sm remove-field">' +
            '<i class="fas fa-trash"></i>' +
            '</button>' +
            '</div>' +
            '</div>';

        $('#featuresContainer').append(featureHtml);
    });
}

/**
 * تفعيل رفع الملفات بالسحب والإفلات
 */
function initializeDragAndDrop() {
    $('.file-upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('.file-upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('.file-upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');

        var files = e.originalEvent.dataTransfer.files;
        var input = $(this).find('input[type="file"]')[0];

        if (input && files.length > 0) {
            input.files = files;
            $(input).trigger('change');
        }
    });

    $('.file-upload-area').on('click', function() {
        $(this).find('input[type="file"]').click();
    });
}

/**
 * تفعيل التحقق من النماذج
 */
function initializeFormValidation() {
    // تخصيص رسائل التحقق
    $('form').on('submit', function(e) {
        var form = this;
        var isValid = true;

        // التحقق من الحقول المطلوبة
        $(form).find('[required]').each(function() {
            if (!$(this).val().trim()) {
                isValid = false;
                $(this).addClass('is-invalid');

                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">هذا الحقل مطلوب</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        });

        // التحقق من البريد الإلكتروني
        $(form).find('input[type="email"]').each(function() {
            var email = $(this).val().trim();
            if (email && !isValidEmail(email)) {
                isValid = false;
                $(this).addClass('is-invalid');

                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">البريد الإلكتروني غير صحيح</div>');
                }
            }
        });

        // التحقق من تطابق كلمات المرور
        var password = $(form).find('input[name="password"]').val();
        var confirmPassword = $(form).find('input[name="confirm_password"]').val();

        if (password && confirmPassword && password !== confirmPassword) {
            isValid = false;
            $(form).find('input[name="confirm_password"]').addClass('is-invalid');

            if (!$(form).find('input[name="confirm_password"]').next('.invalid-feedback').length) {
                $(form).find('input[name="confirm_password"]').after('<div class="invalid-feedback">كلمات المرور غير متطابقة</div>');
            }
        }

        if (!isValid) {
            e.preventDefault();
            showNotification('يرجى تصحيح الأخطاء قبل المتابعة', 'error');
        }
    });

    // إزالة رسائل الخطأ عند الكتابة
    $('input, select, textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).next('.invalid-feedback').remove();
    });
}

/**
 * دوال مساعدة
 */

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// عرض الإشعارات
function showNotification(message, type = 'info', duration = 5000) {
    var alertClass = 'alert-' + (type === 'error' ? 'danger' : type);
    var icon = type === 'success' ? 'check-circle' : 
               type === 'error' ? 'exclamation-triangle' : 
               type === 'warning' ? 'exclamation-triangle' : 'info-circle';

    var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
        '<i class="fas fa-' + icon + ' me-2"></i>' + message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
        '</div>');

    $('body').append(notification);

    setTimeout(function() {
        notification.alert('close');
    }, duration);
}

// تنسيق الأرقام
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// تنسيق التاريخ
function formatDate(dateString, format = 'full') {
    var date = new Date(dateString);

    if (format === 'full') {
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } else if (format === 'short') {
        return date.toLocaleDateString('ar-SA');
    }

    return date.toLocaleDateString('ar-SA');
}

// إرسال طلب AJAX
function sendAjaxRequest(url, data, method = 'POST') {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: url,
            type: method,
            data: data,
            dataType: 'json',
            beforeSend: function() {
                showLoadingOverlay();
            },
            success: function(response) {
                hideLoadingOverlay();
                resolve(response);
            },
            error: function(xhr, status, error) {
                hideLoadingOverlay();
                reject({ xhr, status, error });
            }
        });
    });
}

// عرض شاشة التحميل
function showLoadingOverlay() {
    if (!$('.loading-overlay').length) {
        var overlay = $('<div class="loading-overlay">' +
            '<div class="loading-spinner"></div>' +
            '</div>');
        $('body').append(overlay);
    }
}

// إخفاء شاشة التحميل
function hideLoadingOverlay() {
    $('.loading-overlay').remove();
}

// تأكيد الحذف
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return new Promise((resolve) => {
        Swal.fire({
            title: 'تأكيد الحذف',
            text: message,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74c3c',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            resolve(result.isConfirmed);
        });
    });
}

// تغيير الصورة الرئيسية
function changeMainImage(imageSrc, thumbnailElement) {
    $('#mainImage').attr('src', imageSrc);
    $('.car-thumbnail').removeClass('active');
    $(thumbnailElement).addClass('active');
}

// تصدير البيانات إلى Excel
function exportToExcel(tableId, filename = 'data') {
    var table = document.getElementById(tableId);
    var wb = XLSX.utils.table_to_book(table, { sheet: "Sheet1" });
    XLSX.writeFile(wb, filename + '.xlsx');
}

// طباعة الجدول
function printTable(tableId) {
    var printContent = document.getElementById(tableId).outerHTML;
    var printWindow = window.open('', '', 'height=600,width=800');

    printWindow.document.write('<html><head><title>طباعة</title>');
    printWindow.document.write('<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">');
    printWindow.document.write('<style>body{font-family: Arial, sans-serif; direction: rtl;} .btn, .no-print{display: none;}</style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write(printContent);
    printWindow.document.write('</body></html>');

    printWindow.document.close();
    printWindow.print();
}

// حفظ الإعدادات في localStorage
function saveSettingToLocal(key, value) {
    localStorage.setItem('dashboard_' + key, JSON.stringify(value));
}

// استرجاع الإعدادات من localStorage
function getSettingFromLocal(key, defaultValue = null) {
    var value = localStorage.getItem('dashboard_' + key);
    return value ? JSON.parse(value) : defaultValue;
}

// تحديث العداد في الوقت الفعلي
function startCounterAnimation() {
    $('.stat-number').each(function() {
        var $this = $(this);
        var countTo = parseInt($this.text().replace(/,/g, ''));

        $({ countNum: 0 }).animate({
            countNum: countTo
        }, {
            duration: 2000,
            easing: 'swing',
            step: function() {
                $this.text(formatNumber(Math.floor(this.countNum)));
            },
            complete: function() {
                $this.text(formatNumber(this.countNum));
            }
        });
    });
}

// تفعيل الرسوم البيانية (إذا كانت متوفرة)
function initializeCharts() {
    // يمكن إضافة Chart.js هنا لاحقاً
    if (typeof Chart !== 'undefined') {
        // إعداد الرسوم البيانية
    }
}

// معالجة الأخطاء العامة
window.addEventListener('error', function(e) {
    console.error('خطأ في JavaScript:', e.error);
    showNotification('حدث خطأ غير متوقع', 'error');
});

// معالجة الأخطاء في AJAX
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    console.error('خطأ في AJAX:', thrownError);
    hideLoadingOverlay();

    if (xhr.status === 403) {
        showNotification('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'error');
    } else if (xhr.status === 500) {
        showNotification('خطأ في الخادم. يرجى المحاولة مرة أخرى', 'error');
    } else {
        showNotification('حدث خطأ في الاتصال', 'error');
    }
});