<?php

/**
 * صفحة تعديل السيارة
 * Edit Car Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// عنوان الصفحة
$pageTitle = 'تعديل السيارة';

$error = '';
$success = '';
$car = null;

// التحقق من وجود ID السيارة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$carId = intval($_GET['id']);

// استرجاع بيانات السيارة
try {
    $car = $db->Fetch("SELECT * FROM cars WHERE car_id = :car_id", ['car_id' => $carId]);
    if (!$car) {
        header('Location: index.php');
        exit;
    }
} catch (Exception $e) {
    $error = 'خطأ في استرجاع بيانات السيارة: ' . $e->getMessage();
}

// استرجاع بيانات الصور
$images = [];
try {
    $images = $db->FetchAll("SELECT * FROM car_images WHERE car_id = :car_id ORDER BY is_main DESC, image_id", ['car_id' => $carId]);
} catch (Exception $e) {
    $error = $error . '; خطأ في استرجاع بيانات الصور: ' . $e->getMessage();
}

// استرجاع بيانات الفيديو
$videos = [];
try {
    $videos = $db->FetchAll("SELECT * FROM car_videos WHERE car_id = :car_id ORDER BY video_id", ['car_id' => $carId]);
} catch (Exception $e) {
    $error = $error . '; خطأ في استرجاع بيانات الفيديو: ' . $e->getMessage();
}

// استرجاع التصنيفات
$categories = [];
try {
    $categories = $db->FetchAll("SELECT * FROM car_categories ORDER BY category_ar");
} catch (Exception $e) {
    // في حالة عدم وجود الجدول، نستخدم مصفوفة فارغة
    $categories = [];
    error_log("خطأ في استرجاع التصنيفات: " . $e->getMessage());
}

// قائمة أحجام المحركات الثابتة
$engineSizes = [
    ['engine_size' => '1.0L'],
    ['engine_size' => '1.2L'],
    ['engine_size' => '1.25L'],
    ['engine_size' => '1.4L'],
    ['engine_size' => '1.5L'],
    ['engine_size' => '1.6L'],
    ['engine_size' => '1.8L'],
    ['engine_size' => '2.0L'],
    ['engine_size' => '2.4L'],
    ['engine_size' => '2.5L'],
    ['engine_size' => '2.7L'],
    ['engine_size' => '3.0L'],
    ['engine_size' => '3.3L'],
    ['engine_size' => '3.5L'],
    ['engine_size' => '3.6L'],
    ['engine_size' => '3.8L'],
    ['engine_size' => '4.6L'],
    ['engine_size' => '5.0L']
];

// قائمة الألوان الثابتة
$colors = [
    ['color_ar' => 'كل الالوان', 'color_en' => 'All Colors'],
    ['color_ar' => 'أبيض', 'color_en' => 'White'],
    ['color_ar' => 'أسود', 'color_en' => 'Black'],
    ['color_ar' => 'رمادي', 'color_en' => 'Gray'],
    ['color_ar' => 'فضي', 'color_en' => 'Silver'],
    ['color_ar' => 'أحمر', 'color_en' => 'Red'],
    ['color_ar' => 'أزرق', 'color_en' => 'Blue'],
    ['color_ar' => 'أخضر', 'color_en' => 'Green'],
    ['color_ar' => 'بني', 'color_en' => 'Brown'],
    ['color_ar' => 'ذهبي', 'color_en' => 'Gold'],
    ['color_ar' => 'برتقالي', 'color_en' => 'Orange'],
    ['color_ar' => "بنفسجي", 'color_en' => 'Purple'],
    ['color_ar' => "أصفر", 'color_en' => 'Yellow'],
    ['color_ar' => "وردي", 'color_en' => 'Pink'],
    ['color_ar' => "كحلي", 'color_en' => 'Navy Blue']
];


// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        // جمع البيانات من النموذج
        $carData = [
            'brand_ar' => sanitizeInput($_POST['brand_ar'] ?? ''),
            'brand_en' => sanitizeInput($_POST['brand_en'] ?? ''),
            'model_ar' => sanitizeInput($_POST['model_ar'] ?? ''),
            'model_en' => sanitizeInput($_POST['model_en'] ?? ''),
            'year' => intval($_POST['year'] ?? 0),
            'price' => floatval($_POST['price'] ?? 0),
            'currency' => sanitizeInput($_POST['currency'] ?? 'USD'),
            'mileage' => intval($_POST['mileage'] ?? 0),
            'transmission_ar' => sanitizeInput($_POST['transmission_ar'] ?? ''),
            'transmission_en' => sanitizeInput($_POST['transmission_en'] ?? ''),
            'fuel_type_ar' => sanitizeInput($_POST['fuel_type_ar'] ?? ''),
            'fuel_type_en' => sanitizeInput($_POST['fuel_type_en'] ?? ''),
            'engine_size' => sanitizeInput($_POST['engine_size'] ?? ''),
            'color_ar' => sanitizeInput($_POST['color_ar'] ?? ''),
            'color_en' => sanitizeInput($_POST['color_en'] ?? ''),
            'doors' => intval($_POST['doors'] ?? 4),
            'seats' => intval($_POST['seats'] ?? 5),
            'category_id' => intval($_POST['category_id'] ?? 0),
            'description_ar' => sanitizeInput($_POST['description_ar'] ?? ''),
            'description_en' => sanitizeInput($_POST['description_en'] ?? ''),
            'location_ar' => sanitizeInput($_POST['location_ar'] ?? ''),
            'location_en' => sanitizeInput($_POST['location_en'] ?? ''),
            'is_sold' => isset($_POST['is_sold']) ? 1 : 0,
            'is_display_home' => isset($_POST['is_display_home']) ? 1 : 0,
            'car_id' => $carId
        ];

        // التحقق من صحة البيانات المطلوبة
        if (
            empty($carData['brand_ar']) || empty($carData['model_ar']) ||
            $carData['year'] <= 0 || $carData['price'] < 0
        ) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                // تحديث بيانات السيارة
                $stmt = "UPDATE cars SET 
                    brand_ar = :brand_ar, brand_en = :brand_en,
                    model_ar = :model_ar, model_en = :model_en,
                    year = :year, price = :price, currency = :currency,
                    mileage = :mileage, transmission_ar = :transmission_ar, transmission_en = :transmission_en,
                    fuel_type_ar = :fuel_type_ar, fuel_type_en = :fuel_type_en,
                    engine_size = :engine_size, color_ar = :color_ar, color_en = :color_en,
                    doors = :doors, seats = :seats, category_id = :category_id,
                    description_ar = :description_ar, description_en = :description_en,
                    location_ar = :location_ar, location_en = :location_en,
                    is_sold = :is_sold, is_display_home = :is_display_home, updated_at = NOW()
                    WHERE car_id = :car_id";

                $db->Update($stmt, $carData);

                // إنشاء مجلد الصور
                $uploadDir = UPLOAD_PATH . 'cars/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // رفع الصورة الرئيسية الجديدة
                if (isset($_FILES['main_image']) && !empty($_FILES['main_image']['name'])) {
                    $uploadResult = uploadFile($_FILES['main_image'], $uploadDir);
                    if ($uploadResult['success']) {
                        // حذف الصورة الرئيسية القديمة
                        $oldMainImage = $db->Fetch("SELECT image_url FROM car_images WHERE car_id = :car_id AND is_main = 1", ['car_id' => $carId]);
                        if ($oldMainImage) {
                            $oldImagePath = UPLOAD_PATH . $oldMainImage['image_url'];
                            if (file_exists($oldImagePath)) {
                                unlink($oldImagePath);
                            }
                            $db->Remove("DELETE FROM car_images WHERE car_id = :car_id AND is_main = 1", ['car_id' => $carId]);
                        }

                        // إدراج الصورة الرئيسية الجديدة
                        $imageStmt = "INSERT INTO car_images (car_id, image_url, is_main) VALUES (:car_id, :image_url, :is_main)";
                        $db->Insert($imageStmt, [
                            'car_id' => $carId,
                            'image_url' => 'cars/' . $uploadResult['filename'],
                            'is_main' => 1
                        ]);
                    }
                }

                // رفع الصور الإضافية الجديدة
                if (isset($_FILES['additional_images']) && !empty($_FILES['additional_images']['name'][0])) {
                    foreach ($_FILES['additional_images']['name'] as $key => $fileName) {
                        if (!empty($fileName)) {
                            $fileData = [
                                'name' => $_FILES['additional_images']['name'][$key],
                                'type' => $_FILES['additional_images']['type'][$key],
                                'tmp_name' => $_FILES['additional_images']['tmp_name'][$key],
                                'error' => $_FILES['additional_images']['error'][$key],
                                'size' => $_FILES['additional_images']['size'][$key]
                            ];

                            $uploadResult = uploadFile($fileData, $uploadDir);
                            if ($uploadResult['success']) {
                                // إدراج الصورة الإضافية
                                $imageStmt = "INSERT INTO car_images (car_id, image_url, is_main) VALUES (:car_id, :image_url, :is_main)";
                                $db->Insert($imageStmt, [
                                    'car_id' => $carId,
                                    'image_url' => 'cars/' . $uploadResult['filename'],
                                    'is_main' => 0
                                ]);
                            }
                        }
                    }
                }

                // رفع الفيديو الجديد
                if (isset($_FILES['car_video']) && !empty($_FILES['car_video']['name'])) {
                    $uploadResult = uploadFile($_FILES['car_video'], $uploadDir);
                    if ($uploadResult['success']) {
                        // حذف الفيديو القديم إذا وجد
                        $oldVideo = $db->Fetch("SELECT video_id, video_url FROM car_videos WHERE car_id = :car_id", ['car_id' => $carId]);
                        if ($oldVideo) {
                            $oldVideoPath = UPLOAD_PATH . $oldVideo['video_url'];
                            if (file_exists($oldVideoPath)) {
                                unlink($oldVideoPath);
                            }
                            $db->Remove("DELETE FROM car_videos WHERE car_id = :car_id", ['car_id' => $carId]);
                        }

                        // إدراج الفيديو الجديد
                        $videoStmt = "INSERT INTO car_videos (car_id, video_url) VALUES (:car_id, :video_url)";
                        $db->Insert($videoStmt, [
                            'car_id' => $carId,
                            'video_url' => 'cars/' . $uploadResult['filename']
                        ]);
                    }
                }

                // تحديث المميزات
                if (isset($_POST['features']) && is_array($_POST['features'])) {
                    // حذف المميزات القديمة
                    $db->Remove("DELETE FROM car_features WHERE car_id = :car_id", ['car_id' => $carId]);

                    // إضافة المميزات الجديدة
                    foreach ($_POST['features'] as $feature) {
                        if (!empty($feature['type_ar']) && !empty($feature['name_ar'])) {
                            $featureStmt = "INSERT INTO car_features (
                                car_id, feature_type_ar, feature_type_en,
                                feature_name_ar, feature_name_en,
                                feature_value_ar, feature_value_en
                            ) VALUES (
                                :car_id, :feature_type_ar, :feature_type_en,
                                :feature_name_ar, :feature_name_en,
                                :feature_value_ar, :feature_value_en
                            )";

                            $db->Insert($featureStmt, [
                                'car_id' => $carId,
                                'feature_type_ar' => sanitizeInput($feature['type_ar'] ?? ''),
                                'feature_type_en' => sanitizeInput($feature['type_en'] ?? ''),
                                'feature_name_ar' => sanitizeInput($feature['name_ar'] ?? ''),
                                'feature_name_en' => sanitizeInput($feature['name_en'] ?? ''),
                                'feature_value_ar' => sanitizeInput($feature['value_ar'] ?? ''),
                                'feature_value_en' => sanitizeInput($feature['value_en'] ?? '')
                            ]);
                        }
                    }
                }

                $success = 'تم تحديث السيارة بنجاح';

                // إعادة استرجاع البيانات المحدثة
                $car = $db->Fetch("SELECT * FROM cars WHERE car_id = :car_id", ['car_id' => $carId]);
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء تحديث السيارة: ' . $e->getMessage();
            }
        }
    }
}

// استرجاع مميزات السيارة
$features = [];
if ($car) {
    $features = $db->FetchAll("SELECT * FROM car_features WHERE car_id = :car_id ORDER BY feature_id", ['car_id' => $carId]);
}

// استرجاع صور السيارة
$images = [];
if ($car) {
    $images = $db->FetchAll("SELECT * FROM car_images WHERE car_id = :car_id ORDER BY is_main DESC", ['car_id' => $carId]);
}

include '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تعديل السيارة</h1>
            <p class="text-muted">تعديل بيانات السيارة والصور والمميزات</p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
            <a href="view.php?id=<?php echo $carId; ?>" class="btn btn-outline-primary">
                <i class="fas fa-eye me-2"></i>
                عرض السيارة
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نموذج تعديل السيارة -->
    <form method="POST" enctype="multipart/form-data" id="editCarForm">
        <?php echo getCSRFTokenField(); ?>

        <div class="row">
            <!-- القسم الأيسر - بيانات السيارة -->
            <div class="col-lg-8">
                <!-- المعلومات الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-car me-2"></i>
                            المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- الماركة -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الماركة (بالعربية) <span class="text-danger">*</span></label>
                                <select class="form-control" name="brand_ar" required>
                                    <option value="كيا" <?php echo htmlspecialchars($car['brand_ar'] == 'كيا') ? 'selected' : ''; ?>>كيا</option>
                                    <option value="تويوتا" <?php echo htmlspecialchars($car['brand_ar'] == 'تويوتا') ? 'selected' : ''; ?>>تويوتا</option>
                                    <option value="نيسان" <?php echo htmlspecialchars($car['brand_ar'] == 'نيسان') ? 'selected' : ''; ?>>نيسان</option>
                                    <option value="هيونداي" <?php echo htmlspecialchars($car['brand_ar'] == 'هيونداي') ? 'selected' : ''; ?>>هونداي</option>
                                    <option value="جيب" <?php echo htmlspecialchars($car['brand_ar'] == 'جيب') ? 'selected' : ''; ?>>جيب</option>
                                    <option value="فورد" <?php echo htmlspecialchars($car['brand_ar'] == 'فورد') ? 'selected' : ''; ?>>فورد</option>
                                    <option value="جي ام سي" <?php echo htmlspecialchars($car['brand_ar'] == 'جي ام سي') ? 'selected' : ''; ?>>جي ام سي</option>
                                    <option value="شيفروليه" <?php echo htmlspecialchars($car['brand_ar'] == 'شيفروليه') ? 'selected' : ''; ?>>شيفروليه</option>
                                    <option value="بي واي دي" <?php echo htmlspecialchars($car['brand_ar'] == 'بي واي دي') ? 'selected' : ''; ?>>بي واي دي</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الماركة (بالإنجليزية)</label>
                                <select class="form-control" name="brand_en" required>
                                    <option value="Kia" <?php echo htmlspecialchars($car['brand_en'] == 'Kia') ? 'selected' : ''; ?>>Kia</option>
                                    <option value="Toyota" <?php echo htmlspecialchars($car['brand_en'] == 'Toyota') ? 'selected' : ''; ?>>Toyota</option>
                                    <option value="Nissan" <?php echo htmlspecialchars($car['brand_en'] == 'Nissan') ? 'selected' : ''; ?>>Nissan</option>
                                    <option value="Hyundai" <?php echo htmlspecialchars($car['brand_en'] == 'Hyundai') ? 'selected' : ''; ?>>Hyundai</option>
                                    <option value="Jeep" <?php echo htmlspecialchars($car['brand_en'] == 'Jeep') ? 'selected' : ''; ?>>Jeep</option>
                                    <option value="Ford" <?php echo htmlspecialchars($car['brand_en'] == 'Ford') ? 'selected' : ''; ?>>Ford</option>
                                    <option value="GMC" <?php echo htmlspecialchars($car['brand_en'] == 'GMC') ? 'selected' : ''; ?>>GMC</option>
                                    <option value="Chevrolet" <?php echo htmlspecialchars($car['brand_en'] == 'Chevrolet') ? 'selected' : ''; ?>>Chevrolet</option>
                                    <option value="BYD" <?php echo htmlspecialchars($car['brand_en'] == 'BYD') ? 'selected' : ''; ?>>BYD</option>
                                </select>
                            </div>

                            <!-- الموديل -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموديل (بالعربية) <span class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control"
                                    name="model_ar"
                                    value="<?php echo htmlspecialchars($car['model_ar'] ?? ''); ?>"
                                    placeholder="مثال: كامري"
                                    required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموديل (بالإنجليزية)</label>
                                <input type="text"
                                    class="form-control"
                                    name="model_en"
                                    value="<?php echo htmlspecialchars($car['model_en'] ?? ''); ?>"
                                    placeholder="Example: Camry">
                            </div>

                            <!-- السنة والتصنيف -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سنة الصنع <span class="text-danger">*</span></label>
                                <input type="number"
                                    class="form-control"
                                    name="year"
                                    value="<?php echo $car['year'] ?? ''; ?>"
                                    min="1950"
                                    max="<?php echo date('Y') + 1; ?>"
                                    required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">التصنيف</label>
                                <select class="form-select" name="category_id">
                                    <option value="0">بدون تصنيف</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['category_id']; ?>"
                                            <?php echo ($car['category_id'] == $category['category_id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['category_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل السيارة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            تفاصيل السيارة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- المسافة المقطوعة -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المسافة المقطوعة (كم)</label>
                                <input type="number"
                                    class="form-control"
                                    name="mileage"
                                    value="<?php echo $car['mileage'] ?? ''; ?>"
                                    min="0"
                                    placeholder="مثال: 50000">
                            </div>

                            <!-- حجم المحرك -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حجم المحرك</label>
                                <select class="form-select" name="engine_size">
                                    <option value="">اختر حجم المحرك</option>
                                    <?php foreach ($engineSizes as $size): ?>
                                        <option value="<?php echo htmlspecialchars($size['engine_size']); ?>"
                                            <?php echo (isset($car['engine_size']) && $car['engine_size'] == $size['engine_size']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($size['engine_size']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- ناقل الحركة -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ناقل الحركة (بالعربية)</label>
                                <select class="form-select" name="transmission_ar" id="transmission_ar" onchange="updateTransmissionEn()">
                                    <option value="">اختر نوع ناقل الحركة</option>
                                    <option value="أوتوماتيك" <?php echo (isset($car['transmission_ar']) && $car['transmission_ar'] == 'أوتوماتيك') ? 'selected' : ''; ?>>أوتوماتيك</option>
                                    <option value="CVT" <?php echo (isset($car['transmission_ar']) && $car['transmission_ar'] == 'CVT') ? 'selected' : ''; ?>>CVT</option>
                                    <option value="DCT" <?php echo (isset($car['transmission_ar']) && $car['transmission_ar'] == 'DCT') ? 'selected' : ''; ?>>DCT</option>
                                    <option value="يدوي" <?php echo (isset($car['transmission_ar']) && $car['transmission_ar'] == 'يدوي') ? 'selected' : ''; ?>>يدوي</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">ناقل الحركة (بالإنجليزية)</label>
                                <select class="form-select" name="transmission_en" id="transmission_en">
                                    <option value="">سيتم تحديدها تلقائياً</option>
                                    <option value="Automatic" <?php echo (isset($car['transmission_en']) && $car['transmission_en'] == 'Automatic') ? 'selected' : ''; ?>>Automatic</option>
                                    <option value="CVT" <?php echo (isset($car['transmission_en']) && $car['transmission_en'] == 'CVT') ? 'selected' : ''; ?>>CVT</option>
                                    <option value="DCT" <?php echo (isset($car['transmission_en']) && $car['transmission_en'] == 'DCT') ? 'selected' : ''; ?>>DCT</option>
                                    <option value="Manual" <?php echo (isset($car['transmission_en']) && $car['transmission_en'] == 'Manual') ? 'selected' : ''; ?>>Manual</option>
                                </select>
                            </div>

                            <!-- نوع الوقود -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الوقود (بالعربية)</label>
                                <input type="text"
                                    class="form-control"
                                    name="fuel_type_ar"
                                    value="<?php echo htmlspecialchars($car['fuel_type_ar'] ?? ''); ?>"
                                    placeholder="مثال: بنزين">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الوقود (بالإنجليزية)</label>
                                <input type="text"
                                    class="form-control"
                                    name="fuel_type_en"
                                    value="<?php echo htmlspecialchars($car['fuel_type_en'] ?? ''); ?>"
                                    placeholder="Example: Gasoline">
                            </div>

                            <!-- اللون -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اللون (بالعربية)</label>
                                <select class="form-select" name="color_ar" id="color_ar" onchange="updateColorEn()">
                                    <option value="">اختر اللون</option>
                                    <?php foreach ($colors as $color): ?>
                                        <option value="<?php echo htmlspecialchars($color['color_ar']); ?>"
                                            <?php echo (isset($car['color_ar']) && $car['color_ar'] == $color['color_ar']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($color['color_ar']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">اللون (بالإنجليزية)</label>
                                <select class="form-select" name="color_en" id="color_en">
                                    <option value="">سيتم تحديدها تلقائياً</option>
                                    <?php foreach ($colors as $color): ?>
                                        <option value="<?php echo htmlspecialchars($color['color_en']); ?>"
                                            <?php echo (isset($car['color_en']) && $car['color_en'] == $color['color_en']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($color['color_en']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- عدد الأبواب والمقاعد -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">عدد الأبواب</label>
                                <select class="form-select" name="doors">
                                    <option value="2" <?php echo ($car['doors'] == 2) ? 'selected' : ''; ?>>2 أبواب</option>
                                    <option value="4" <?php echo ($car['doors'] == 4) ? 'selected' : ''; ?>>4 أبواب</option>
                                    <option value="5" <?php echo ($car['doors'] == 5) ? 'selected' : ''; ?>>5 أبواب</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">عدد المقاعد</label>
                                <select class="form-select" name="seats">
                                    <option value="2" <?php echo ($car['seats'] == 2) ? 'selected' : ''; ?>>2 مقاعد</option>
                                    <option value="3" <?php echo ($car['seats'] == 3) ? 'selected' : ''; ?>>3 مقاعد</option>
                                    <option value="4" <?php echo ($car['seats'] == 4) ? 'selected' : ''; ?>>4 مقاعد</option>
                                    <option value="5" <?php echo ($car['seats'] == 5) ? 'selected' : ''; ?>>5 مقاعد</option>
                                    <option value="7" <?php echo ($car['seats'] == 7) ? 'selected' : ''; ?>>7 مقاعد</option>
                                    <option value="8" <?php echo ($car['seats'] == 8) ? 'selected' : ''; ?>>8 مقاعد</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الوصف والموقع -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-align-left me-2"></i>
                            الوصف والموقع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- الوصف -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوصف (بالعربية)</label>
                                <textarea class="form-control"
                                    name="description_ar"
                                    rows="4"
                                    placeholder="وصف تفصيلي للسيارة..."><?php echo htmlspecialchars($car['description_ar'] ?? ''); ?></textarea>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوصف (بالإنجليزية)</label>
                                <textarea class="form-control"
                                    name="description_en"
                                    rows="4"
                                    placeholder="Detailed car description..."><?php echo htmlspecialchars($car['description_en'] ?? ''); ?></textarea>
                            </div>

                            <!-- الموقع -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموقع (بالعربية)</label>
                                <input type="text"
                                    class="form-control"
                                    name="location_ar"
                                    value="<?php echo htmlspecialchars($car['location_ar'] ?? ''); ?>"
                                    placeholder="مثال: بغداد، العراق">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموقع (بالإنجليزية)</label>
                                <input type="text"
                                    class="form-control"
                                    name="location_en"
                                    value="<?php echo htmlspecialchars($car['location_en'] ?? ''); ?>"
                                    placeholder="Example: Baghdad, Iraq">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مميزات السيارة -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star me-2"></i>
                            مميزات السيارة
                        </h5>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addFeature()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة ميزة
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="featuresContainer">
                            <?php foreach ($features as $index => $feature): ?>
                                <div class="feature-item border p-3 mb-3 rounded" id="feature_<?php echo $index; ?>">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">ميزة <?php echo $index + 1; ?></h6>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFeature(<?php echo $index; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">نوع الميزة (عربي)</label>
                                            <input type="text" class="form-control" name="features[<?php echo $index; ?>][type_ar]"
                                                value="<?php echo htmlspecialchars($feature['feature_type_ar']); ?>" placeholder="مثال: الأمان">
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">نوع الميزة (إنجليزي)</label>
                                            <input type="text" class="form-control" name="features[<?php echo $index; ?>][type_en]"
                                                value="<?php echo htmlspecialchars($feature['feature_type_en']); ?>" placeholder="Example: Safety">
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">اسم الميزة (عربي)</label>
                                            <input type="text" class="form-control" name="features[<?php echo $index; ?>][name_ar]"
                                                value="<?php echo htmlspecialchars($feature['feature_name_ar']); ?>" placeholder="مثال: وسائد هوائية">
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">اسم الميزة (إنجليزي)</label>
                                            <input type="text" class="form-control" name="features[<?php echo $index; ?>][name_en]"
                                                value="<?php echo htmlspecialchars($feature['feature_name_en']); ?>" placeholder="Example: Airbags">
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">قيمة الميزة (عربي)</label>
                                            <input type="text" class="form-control" name="features[<?php echo $index; ?>][value_ar]"
                                                value="<?php echo htmlspecialchars($feature['feature_value_ar']); ?>" placeholder="مثال: 6 وسائد">
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">قيمة الميزة (إنجليزي)</label>
                                            <input type="text" class="form-control" name="features[<?php echo $index; ?>][value_en]"
                                                value="<?php echo htmlspecialchars($feature['feature_value_en']); ?>" placeholder="Example: 6 airbags">
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- القسم الأيمن - السعر والصور -->
            <div class="col-lg-4">
                <!-- السعر والحالة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tag me-2"></i>
                            السعر والحالة
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- السعر -->
                        <div class="mb-3">
                            <label class="form-label">السعر <span class="text-danger">*</span></label>
                            <input type="number"
                                class="form-control"
                                name="price"
                                value="<?php echo $car['price'] ?? ''; ?>"
                                min="0"
                                step="0.01"
                                required>
                        </div>

                        <!-- العملة -->
                        <div class="mb-3">
                            <label class="form-label">العملة</label>
                            <select class="form-select" name="currency">
                                <option value="USD" <?php echo ($car['currency'] == 'USD') ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                <option value="IQD" <?php echo ($car['currency'] == 'IQD') ? 'selected' : ''; ?>>دينار عراقي (IQD)</option>
                                <option value="EUR" <?php echo ($car['currency'] == 'EUR') ? 'selected' : ''; ?>>يورو (EUR)</option>
                            </select>
                        </div>

                        <!-- حالة البيع -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input"
                                    type="checkbox"
                                    name="is_sold"
                                    id="is_sold"
                                    <?php echo ($car['is_sold']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_sold">
                                    السيارة غير متاحة
                                </label>
                            </div>
                        </div>
                        <!-- حالة عرضها في الرئيسية -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input"
                                    type="checkbox"
                                    name="is_display_home"
                                    id="is_display_home"
                                    <?php echo ($car['is_display_home']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_display_home">
                                    عرض السيارة في الرئيسية
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصور الحالية -->
                <?php if (!empty($images)): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-images me-2"></i>
                                الصور الحالية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($images as $image): ?>
                                    <div class="col-6 mb-2">
                                        <div class="position-relative">
                                            <img src="<?php echo UPLOAD_PATH . $image['image_url']; ?>"
                                                class="img-thumbnail w-100"
                                                style="height: 100px; object-fit: cover;">
                                            <?php if ($image['is_main']): ?>
                                                <span class="badge bg-primary position-absolute top-0 start-0 m-1">رئيسية</span>
                                            <?php endif; ?>
                                            <button type="button"
                                                class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                                                onclick="deleteImage(<?php echo $image['image_id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- إضافة صورة رئيسية جديدة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-image me-2"></i>
                            تغيير الصورة الرئيسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">صورة رئيسية جديدة</label>
                            <input type="file"
                                class="form-control"
                                name="main_image"
                                accept="image/*,.webp"
                                onchange="previewMainImage(this)">
                            <small class="form-text text-muted">
                                سيتم استبدال الصورة الرئيسية الحالية. الصيغ المدعومة: JPG, PNG, GIF, WebP
                            </small>
                        </div>

                        <!-- معاينة الصورة الرئيسية -->
                        <div id="mainImagePreview" class="text-center"></div>
                    </div>
                </div>

                <!-- إضافة صور إضافية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-images me-2"></i>
                            إضافة صور جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">صور إضافية جديدة</label>
                            <input type="file"
                                class="form-control"
                                name="additional_images[]"
                                multiple
                                accept="image/*,.webp"
                                onchange="previewAdditionalImages(this)">
                            <small class="form-text text-muted">
                                يمكنك إضافة عدة صور جديدة. الصيغ المدعومة: JPG, PNG, GIF, WebP
                            </small>
                        </div>

                        <!-- معاينة الصور الإضافية -->
                        <div id="additionalImagesPreview" class="row"></div>
                    </div>
                </div>

                <!-- فيديو السيارة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-video me-2"></i>
                            فيديو السيارة
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- الفيديو الحالي -->
                        <?php if (!empty($videos)): ?>
                            <div class="mb-3">
                                <label class="form-label">الفيديو الحالي</label>
                                <div class="embed-responsive embed-responsive-16by9 mb-2">
                                    <video class="embed-responsive-item" controls style="max-width: 100%; height: auto;">
                                        <source src="<?php echo UPLOAD_PATH . $videos[0]['video_url']; ?>" type="video/mp4">
                                        متصفحك لا يدعم تشغيل الفيديو.
                                    </video>
                                </div>
                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteVideo(<?php echo $videos[0]['video_id']; ?>)">
                                    <i class="fas fa-trash me-1"></i>
                                    حذف الفيديو الحالي
                                </button>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label class="form-label">رفع فيديو جديد</label>
                            <input type="file"
                                class="form-control"
                                name="car_video"
                                accept="video/mp4"
                                onchange="previewVideo(this)">
                            <small class="form-text text-muted">
                                يمكنك رفع فيديو جديد للسيارة. الصيغة المدعومة: MP4 (الحد الأقصى 50MB)
                            </small>
                        </div>

                        <!-- معاينة الفيديو -->
                        <div id="videoPreview" class="text-center"></div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="view.php?id=<?php echo $carId; ?>" class="btn btn-outline-info">
                                <i class="fas fa-eye me-2"></i>
                                عرض السيارة
                            </a>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
    let featureIndex = <?php echo count($features); ?>;

    // إضافة ميزة جديدة
    function addFeature() {
        const container = document.getElementById('featuresContainer');
        const featureHtml = `
        <div class="feature-item border p-3 mb-3 rounded" id="feature_${featureIndex}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">ميزة ${featureIndex + 1}</h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFeature(${featureIndex})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="row">
                <div class="col-md-6 mb-2">
                    <label class="form-label">نوع الميزة (عربي)</label>
                    <input type="text" class="form-control" name="features[${featureIndex}][type_ar]" placeholder="مثال: الأمان">
                </div>
                <div class="col-md-6 mb-2">
                    <label class="form-label">نوع الميزة (إنجليزي)</label>
                    <input type="text" class="form-control" name="features[${featureIndex}][type_en]" placeholder="Example: Safety">
                </div>
                <div class="col-md-6 mb-2">
                    <label class="form-label">اسم الميزة (عربي)</label>
                    <input type="text" class="form-control" name="features[${featureIndex}][name_ar]" placeholder="مثال: وسائد هوائية">
                </div>
                <div class="col-md-6 mb-2">
                    <label class="form-label">اسم الميزة (إنجليزي)</label>
                    <input type="text" class="form-control" name="features[${featureIndex}][name_en]" placeholder="Example: Airbags">
                </div>
                <div class="col-md-6 mb-2">
                    <label class="form-label">قيمة الميزة (عربي)</label>
                    <input type="text" class="form-control" name="features[${featureIndex}][value_ar]" placeholder="مثال: 6 وسائد">
                </div>
                <div class="col-md-6 mb-2">
                    <label class="form-label">قيمة الميزة (إنجليزي)</label>
                    <input type="text" class="form-control" name="features[${featureIndex}][value_en]" placeholder="Example: 6 airbags">
                </div>
            </div>
        </div>
    `;

        container.insertAdjacentHTML('beforeend', featureHtml);
        featureIndex++;
    }

    // حذف ميزة
    function removeFeature(index) {
        const feature = document.getElementById(`feature_${index}`);
        if (feature) {
            feature.remove();
        }
    }

    // معاينة الصورة الرئيسية
    function previewMainImage(input) {
        const preview = document.getElementById('mainImagePreview');
        preview.innerHTML = '';

        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `
                <div class="mt-3">
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 200px; object-fit: cover;">
                    <div class="mt-2">
                        <span class="badge bg-primary">الصورة الرئيسية الجديدة</span>
                    </div>
                </div>
            `;
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // معاينة الصور الإضافية
    function previewAdditionalImages(input) {
        const preview = document.getElementById('additionalImagesPreview');
        preview.innerHTML = '';

        if (input.files) {
            Array.from(input.files).forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const col = document.createElement('div');
                    col.className = 'col-6 col-md-4 mb-2';
                    col.innerHTML = `
                    <div class="position-relative">
                        <img src="${e.target.result}" class="img-thumbnail w-100" style="height: 100px; object-fit: cover;">
                        <span class="badge bg-success position-absolute top-0 start-0 m-1">جديدة ${index + 1}</span>
                    </div>
                `;
                    preview.appendChild(col);
                };
                reader.readAsDataURL(file);
            });
        }
    }

    // معاينة الفيديو
    function previewVideo(input) {
        const preview = document.getElementById('videoPreview');
        preview.innerHTML = '';

        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'embed-responsive embed-responsive-16by9';
                div.innerHTML = `
                    <video class="embed-responsive-item" controls style="max-width: 100%; height: auto;">
                        <source src="${e.target.result}" type="video/mp4">
                        متصفحك لا يدعم تشغيل الفيديو.
                    </video>
                `;
                preview.appendChild(div);
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // حذف صورة
    function deleteImage(imageId) {
        if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
            // إنشاء FormData لإرسال رمز CSRF
            const formData = new FormData();
            formData.append('image_id', imageId);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('delete_image.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تحميل قسم الصور فقط بدلاً من الصفحة كاملة
                        location.reload();
                    } else {
                        alert('فشل في حذف الصورة: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ أثناء حذف الصورة');
                });
        }
    }

    // حذف فيديو
    function deleteVideo(videoId) {
        if (confirm('هل أنت متأكد من حذف هذا الفيديو؟')) {
            // إنشاء FormData لإرسال رمز CSRF
            const formData = new FormData();
            formData.append('video_id', videoId);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('delete_video.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إعادة تحميل قسم الفيديو فقط بدلاً من الصفحة كاملة
                        location.reload();
                    } else {
                        alert('فشل في حذف الفيديو: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ أثناء حذف الفيديو');
                });
        }
    }

    // دالة لتحديث قيمة ناقل الحركة بالإنجليزية بناءً على القيمة العربية
    function updateTransmissionEn() {
        var transmissionAr = document.getElementById('transmission_ar');

        // الحصول على القيمة الحالية من القائمة العربية
        var selectedValue = transmissionAr.value;
        var transmissionEn = document.getElementById('transmission_en');

        if (!transmissionAr || !transmissionEn) return;
        // تعيين القيمة الإنجليزية المقابلة تلقائياً
        switch (selectedValue) {
            case 'أوتوماتيك':
                transmissionEn.value = 'Automatic';
                break;
            case 'CVT':
                transmissionEn.value = 'CVT';
                break;
            case 'DCT':
                transmissionEn.value = 'DCT';
                break;
            case 'يدوي':
                transmissionEn.value = 'Manual';
                break;
            default:
                transmissionEn.value = '';
        }
    }

    // استدعاء الدوال عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateTransmissionEn();
    });

    // تحقق من صحة النموذج
    document.getElementById('editCarForm').addEventListener('submit', function(e) {
        const requiredFields = ['brand_ar', 'model_ar', 'year', 'price'];
        let isValid = true;

        requiredFields.forEach(field => {
            const input = document.querySelector(`[name="${field}"]`);
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
    // دالة لتحديث قيمة اللون بالإنجليزية بناءً على القيمة العربية
    function updateColorEn() {
        const colorAr = document.getElementById('color_ar').value;
        const colorEn = document.getElementById('color_en');

        // تعيين القيمة الإنجليزية المقابلة تلقائياً
        switch (colorAr) {
            case 'كل الالوان':
                colorEn.value = 'All Colors';
                break;
            case 'أبيض':
                colorEn.value = 'White';
                break;
            case 'أسود':
                colorEn.value = 'Black';
                break;
            case 'رمادي':
                colorEn.value = 'Gray';
                break;
            case 'فضي':
                colorEn.value = 'Silver';
                break;
            case 'أحمر':
                colorEn.value = 'Red';
                break;
            case 'أزرق':
                colorEn.value = 'Blue';
                break;
            case 'أخضر':
                colorEn.value = 'Green';
                break;
            case 'بني':
                colorEn.value = 'Brown';
                break;
            case 'ذهبي':
                colorEn.value = 'Gold';
                break;
            case 'برتقالي':
                colorEn.value = 'Orange';
                break;
            case "بنفسجي":
                colorEn.value = 'Purple';
                break;
            case "أصفر":
                colorEn.value = 'Yellow';
                break;
            case "وردي":
                colorEn.value = 'Pink';
                break;
            case "كحلي":
                colorEn.value = 'Navy Blue';
                break;
            default:
                colorEn.value = '';
        }
    }
</script>

<?php include '../../includes/footer.php'; ?>