<?php
$currentLang = getCurrentLanguage();
?>

<!-- Submit Review Hero Section -->
<section class="hero-section position-relative text-center" style="padding-top: 100px;">
    <div class="animated-bg" data-url="<?php echo ASSETS_PATH; ?>" style="background-image: url('<?php echo ASSETS_PATH; ?>images/k4-25my-kv-pc.jpg');"></div>
    <div class="container" style="z-index: 2;">
        <div class="row min-vh-80 align-items-center">
            <div class="text-white flex flex-col gap-5 pb-5 w-full position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <h1 class="display-4 fw-bold mb-3 text-white animate__animated animate__fadeInDown" style="text-transform: uppercase; letter-spacing: 1.5px;">
                    <?php echo ($currentLang == 'ar') ? 'شاركنا رأيك' : 'Share Your Experience'; ?>
                </h1>
                <div class="divider mx-auto mb-4" style="height: 4px; width: 70px; background-color: #05141f;"></div>
                <p class="lead mb-4 text-white">
                    <?php echo ($currentLang == 'ar') ? 'رأيك يهمنا ويساعد عملاءنا الآخرين في اتخاذ القرار المناسب' : 'Your opinion matters to us and helps other customers make the right decision'; ?>
                </p>
            </div>
        </div>
    </div>
    <svg data-name="VIDEO GRADIENT" xmlns="http://www.w3.org/2000/svg" class="home-hero__gradient" role="presentation">
        <defs data-v-1bfe21c6="">
            <linearGradient id="DesktopGradient_svg__a" x1=".5" y1=".129" x2=".5" y2=".708" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-opacity="0"></stop>
                <stop offset=".364" stop-opacity=".424"></stop>
                <stop offset=".64" stop-color="#030b11" stop-opacity=".733"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
            <linearGradient id="DesktopGradient_svg__b" x1=".5" y1=".129" x2=".5" y2=".5" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-color="#05141f" stop-opacity="0"></stop>
                <stop offset=".226" stop-color="#05141f" stop-opacity=".082"></stop>
                <stop offset=".512" stop-color="#05141f" stop-opacity=".271"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
        </defs>
        <rect fill="url(#DesktopGradient_svg__a)" width="100%" height="204"></rect>
        <rect fill="url(#DesktopGradient_svg__b)" y="72" width="100%" height="132"></rect>
    </svg>
</section>

<!-- Review Form Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8" data-aos="fade-up">
                <!-- عرض رسائل النجاح والخطأ -->
                <?php if (isset($_SESSION['review_success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong><?php echo ($currentLang == 'ar') ? 'شكراً لك!' : 'Thank you!'; ?></strong>
                        <?php echo $_SESSION['review_success']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['review_success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['review_error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong><?php echo ($currentLang == 'ar') ? 'خطأ!' : 'Error!'; ?></strong>
                        <?php echo $_SESSION['review_error']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php unset($_SESSION['review_error']); ?>
                <?php endif; ?>

                <div class="contact-form bg-light p-4 p-md-5 rounded shadow-sm">
                    <h3 class="mb-4 border-bottom pb-3">
                        <i class="fas fa-edit text-primary me-2"></i>
                        <?php echo ($currentLang == 'ar') ? 'نموذج تسجيل الرأي' : 'Review Submission Form'; ?>
                    </h3>
                    
                    <form id="reviewForm" action="process_submit_review.php" method="post">
                        <div class="row g-3">
                            <!-- اسم العميل -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                           placeholder="<?php echo ($currentLang == 'ar') ? 'الاسم الكامل' : 'Full Name'; ?>" required>
                                    <label for="customer_name">
                                        <?php echo ($currentLang == 'ar') ? 'الاسم الكامل' : 'Full Name'; ?> <span class="text-danger">*</span>
                                    </label>
                                </div>
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="<?php echo ($currentLang == 'ar') ? 'البريد الإلكتروني' : 'Email Address'; ?>" required>
                                    <label for="email">
                                        <?php echo ($currentLang == 'ar') ? 'البريد الإلكتروني' : 'Email Address'; ?> <span class="text-danger">*</span>
                                    </label>
                                </div>
                            </div>

                            <!-- رقم الهاتف -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           placeholder="<?php echo ($currentLang == 'ar') ? 'رقم الهاتف' : 'Phone Number'; ?>">
                                    <label for="phone"><?php echo ($currentLang == 'ar') ? 'رقم الهاتف' : 'Phone Number'; ?></label>
                                </div>
                            </div>

                            <!-- السيارة المشتراة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="car_purchased" name="car_purchased" 
                                           placeholder="<?php echo ($currentLang == 'ar') ? 'السيارة المشتراة' : 'Car Purchased'; ?>">
                                    <label for="car_purchased"><?php echo ($currentLang == 'ar') ? 'السيارة المشتراة' : 'Car Purchased'; ?></label>
                                </div>
                            </div>

                            <!-- تاريخ الشراء -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="purchase_date" name="purchase_date">
                                    <label for="purchase_date"><?php echo ($currentLang == 'ar') ? 'تاريخ الشراء' : 'Purchase Date'; ?></label>
                                </div>
                            </div>

                            <!-- التقييم -->
                            <div class="col-12">
                                <div class="mb-4">
                                    <label class="form-label fw-semibold mb-3">
                                        <?php echo ($currentLang == 'ar') ? 'التقييم' : 'Rating'; ?> <span class="text-danger">*</span>
                                    </label>
                                    <div class="rating-container">
                                        <div class="rating-input d-flex align-items-center gap-2 mb-3">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <input type="radio" id="star<?php echo $i; ?>" name="rating" value="<?php echo $i; ?>" required>
                                                <label for="star<?php echo $i; ?>" class="star-label" data-rating="<?php echo $i; ?>">
                                                    <i class="fas fa-star"></i>
                                                </label>
                                            <?php endfor; ?>
                                        </div>
                                        <div class="rating-display mb-2">
                                            <span class="rating-text text-primary fw-bold"></span>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo ($currentLang == 'ar') ? 'اختر من 1 إلى 5 نجوم' : 'Choose from 1 to 5 stars'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- نص الرأي -->
                            <div class="col-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="review_text" name="review_text" 
                                              placeholder="<?php echo ($currentLang == 'ar') ? 'رأيك وتجربتك' : 'Your Review & Experience'; ?>" 
                                              style="height: 150px" required></textarea>
                                    <label for="review_text">
                                        <?php echo ($currentLang == 'ar') ? 'رأيك وتجربتك' : 'Your Review & Experience'; ?> <span class="text-danger">*</span>
                                    </label>
                                </div>
                                <div class="form-text mb-3">
                                    <?php echo ($currentLang == 'ar') ? 'يرجى كتابة رأيك بصراحة ووضوح لمساعدة العملاء الآخرين' : 'Please write your honest and clear opinion to help other customers'; ?>
                                </div>
                            </div>

                            <!-- أزرار الإرسال -->
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'إرسال الرأي' : 'Submit Review'; ?>
                                </button>
                                <a href="<?php echo createLink('index.php'); ?>" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-arrow-<?php echo ($currentLang == 'ar') ? 'right' : 'left'; ?> me-2"></i>
                                    <?php echo ($currentLang == 'ar') ? 'العودة للرئيسية' : 'Back to Home'; ?>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Why Your Review Matters Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h3 class="mb-4">
                    <?php echo ($currentLang == 'ar') ? 'لماذا رأيك مهم؟' : 'Why Your Review Matters?'; ?>
                </h3>
                <div class="row">
                    <div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-item text-center p-3">
                            <i class="fas fa-users text-primary mb-3" style="font-size: 2.5rem;"></i>
                            <h5><?php echo ($currentLang == 'ar') ? 'مساعدة الآخرين' : 'Help Others'; ?></h5>
                            <p class="text-muted">
                                <?php echo ($currentLang == 'ar') ? 'رأيك يساعد العملاء الآخرين في اتخاذ قرار الشراء' : 'Your opinion helps other customers make purchasing decisions'; ?>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-item text-center p-3">
                            <i class="fas fa-chart-line text-primary mb-3" style="font-size: 2.5rem;"></i>
                            <h5><?php echo ($currentLang == 'ar') ? 'تحسين الخدمة' : 'Improve Service'; ?></h5>
                            <p class="text-muted">
                                <?php echo ($currentLang == 'ar') ? 'ملاحظاتك تساعدنا في تطوير وتحسين خدماتنا' : 'Your feedback helps us develop and improve our services'; ?>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-item text-center p-3">
                            <i class="fas fa-handshake text-primary mb-3" style="font-size: 2.5rem;"></i>
                            <h5><?php echo ($currentLang == 'ar') ? 'بناء الثقة' : 'Build Trust'; ?></h5>
                            <p class="text-muted">
                                <?php echo ($currentLang == 'ar') ? 'الآراء الصادقة تبني الثقة بيننا وبين عملائنا' : 'Honest reviews build trust between us and our customers'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .rating-container {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .rating-input {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-bottom: 15px;
    }

    .rating-input input[type="radio"] {
        display: none;
    }

    .rating-input .star-label {
        font-size: 1.5rem;
        color: #ddd;
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 3px;
        display: inline-block;
        line-height: 1;
    }

    .rating-input .star-label:hover {
        color: #ffc107;
        transform: scale(1.05);
    }

    .rating-input .star-label.active {
        color: #ffc107;
    }

    .rating-display {
        min-height: 30px;
        display: flex;
        align-items: center;
    }

    .rating-text {
        font-size: 1rem;
        display: inline-block;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }

    .feature-item {
        transition: transform 0.3s ease;
    }

    .feature-item:hover {
        transform: translateY(-5px);
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(5, 20, 31, 0.25);
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
    }

    /* تحسين عرض النجوم على الشاشات الصغيرة */
    @media (max-width: 576px) {
        .rating-input .star-label {
            font-size: 1.3rem;
            padding: 2px;
        }
        
        .rating-container {
            padding: 15px;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const stars = document.querySelectorAll('.rating-input .star-label');
        const ratingInputs = document.querySelectorAll('.rating-input input[type="radio"]');
        const ratingText = document.querySelector('.rating-text');
        
        // نصوص التقييم
        const ratingTexts = {
            ar: {
                1: '⭐ ضعيف',
                2: '⭐⭐ مقبول', 
                3: '⭐⭐⭐ جيد',
                4: '⭐⭐⭐⭐ ممتاز',
                5: '⭐⭐⭐⭐⭐ رائع'
            },
            en: {
                1: '⭐ Poor',
                2: '⭐⭐ Fair',
                3: '⭐⭐⭐ Good', 
                4: '⭐⭐⭐⭐ Excellent',
                5: '⭐⭐⭐⭐⭐ Amazing'
            }
        };
        
        const currentLang = '<?php echo $currentLang; ?>';

        // إضافة مستمعي الأحداث للنجوم
        stars.forEach((star, index) => {
            const rating = parseInt(star.getAttribute('data-rating'));
            
            star.addEventListener('mouseenter', function() {
                highlightStars(rating);
                updateRatingText(rating);
            });

            star.addEventListener('click', function() {
                ratingInputs[index].checked = true;
                highlightStars(rating);
                updateRatingText(rating);
                
                // إضافة كلاس active للنجمة المختارة
                stars.forEach(s => s.classList.remove('selected'));
                for(let i = 0; i < rating; i++) {
                    stars[i].classList.add('selected');
                }
            });
        });

        // عند مغادرة منطقة النجوم
        document.querySelector('.rating-input').addEventListener('mouseleave', function() {
            const checkedInput = document.querySelector('.rating-input input[type="radio"]:checked');
            if (checkedInput) {
                const rating = parseInt(checkedInput.value);
                highlightStars(rating);
                updateRatingText(rating);
            } else {
                highlightStars(0);
                updateRatingText(0);
            }
        });

        function highlightStars(rating) {
            stars.forEach((star, index) => {
                const starRating = parseInt(star.getAttribute('data-rating'));
                if (starRating <= rating) {
                    star.style.color = '#ffc107';
                    star.classList.add('active');
                } else {
                    star.style.color = '#e0e0e0';
                    star.classList.remove('active');
                }
            });
        }
        
        function updateRatingText(rating) {
            if (rating > 0) {
                ratingText.textContent = ratingTexts[currentLang][rating];
                ratingText.style.opacity = '1';
            } else {
                ratingText.textContent = '';
                ratingText.style.opacity = '0';
            }
        }

        // التحقق من صحة النموذج
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            const rating = document.querySelector('.rating-input input[type="radio"]:checked');
            if (!rating) {
                e.preventDefault();
                alert('<?php echo ($currentLang == 'ar') ? 'يرجى اختيار تقييم' : 'Please select a rating'; ?>');
                return false;
            }
        });
    });
</script>