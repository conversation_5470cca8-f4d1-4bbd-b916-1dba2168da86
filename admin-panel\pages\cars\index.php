<?php

/**
 * صفحة عرض جميع السيارات
 * Cars Management Index Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// عنوان الصفحة
$pageTitle = 'إدارة السيارات';

// معالجة حذف السيارة
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $carId = intval($_GET['delete']);

    try {
        // حذف صور السيارة من النظام
        $carImages = $db->FetchAll("SELECT image_url FROM car_images WHERE car_id = :car_id", ['car_id' => $carId]);
        foreach ($carImages as $image) {
            deleteFile(UPLOAD_PATH . $image['image_url']);
        }

        // حذف صور السيارة من قاعدة البيانات
        $db->Remove("DELETE FROM car_images WHERE car_id = :car_id", ['car_id' => $carId]);

        // حذف مميزات السيارة
        $db->Remove("DELETE FROM car_features WHERE car_id = :car_id", ['car_id' => $carId]);

        // حذف السيارة
        $db->Remove("DELETE FROM cars WHERE car_id = :car_id", ['car_id' => $carId]);

        $_SESSION['success_message'] = 'تم حذف السيارة بنجاح';
        header('Location: index.php');
        exit();
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء حذف السيارة: ' . $e->getMessage();
    }
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category = isset($_GET['category']) ? intval($_GET['category']) : 0;
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// بناء الاستعلام
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(c.brand_ar LIKE :search OR c.brand_en LIKE :search OR c.model_ar LIKE :search OR c.model_en LIKE :search)";
    $params['search'] = "%$search%";
}

if ($category > 0) {
    $whereConditions[] = "c.category_id = :category";
    $params['category'] = $category;
}

if ($status !== '') {
    $whereConditions[] = "c.is_sold = :status";
    $params['status'] = $status === 'sold' ? 1 : 0;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

try {
    // عدد السيارات الإجمالي
    $totalQuery = "SELECT COUNT(*) as count FROM cars c $whereClause";
    $totalCars = $db->Fetch($totalQuery, $params)['count'];

    // حساب بيانات التقسيم
    $pagination = paginate($totalCars, $limit, $page);

    // استرجاع السيارات
    $carsQuery = "
        SELECT c.*, cat.category_ar, cat.category_en,
               (SELECT image_url FROM car_images WHERE car_id = c.car_id AND is_main = 1 LIMIT 1) as main_image
        FROM cars c 
        LEFT JOIN car_categories cat ON c.category_id = cat.category_id 
        $whereClause
        ORDER BY c.created_at DESC 
        LIMIT $limit OFFSET $offset
    ";

    $cars = $db->FetchAll($carsQuery, $params);

    // استرجاع الفئات للفلتر
    $categories = $db->FetchAll("SELECT * FROM car_categories ORDER BY category_ar");
} catch (Exception $e) {
    $error = "خطأ في استرجاع البيانات: " . $e->getMessage();
    $cars = [];
    $categories = [];
}

// تضمين ملف الرأس
include '../../includes/header.php';
?>

<!-- محتوى صفحة إدارة السيارات -->
<div class="container-fluid p-4">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">إدارة السيارات</h1>
        <a href="add.php" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة سيارة جديدة
        </a>
    </div>

    <!-- نموذج البحث والفلترة -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <!-- البحث -->
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text"
                        class="form-control"
                        name="search"
                        value="<?php echo htmlspecialchars($search); ?>"
                        placeholder="ابحث في الماركة أو الموديل...">
                </div>

                <!-- الفئة -->
                <div class="col-md-3">
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['category_id']; ?>"
                                <?php echo $category == $cat['category_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['category_ar']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- الحالة -->
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="available" <?php echo $status === 'available' ? 'selected' : ''; ?>>متاح</option>
                        <option value="sold" <?php echo $status === 'sold' ? 'selected' : ''; ?>>مباع</option>
                    </select>
                </div>

                <!-- أزرار الفلترة -->
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="index.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول السيارات -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                السيارات (<?php echo number_format($totalCars); ?> سيارة)
            </h5>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-secondary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php elseif (empty($cars)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-car text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">لا توجد سيارات</h4>
                    <p class="text-muted">لم يتم العثور على أي سيارات تطابق معايير البحث</p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة سيارة جديدة
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="carsTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الماركة والموديل</th>
                                <th>السنة</th>
                                <th>السعر</th>
                                <th>الكيلومترات</th>
                                <th>الفئة</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cars as $car): ?>
                                <tr>
                                    <td>
                                        <?php if ($car['main_image']): ?>
                                            <img src="<?php echo UPLOAD_PATH . $car['main_image']; ?>"
                                                alt="<?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar']); ?>"
                                                class="img-thumbnail car-thumb"
                                                style="width: 70px; height: 50px; object-fit: cover; cursor: pointer;"
                                                onclick="showImageModal('<?php echo UPLOAD_PATH . $car['main_image']; ?>')">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center car-thumb"
                                                style="width: 70px; height: 50px;">
                                                <i class="fas fa-car text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($car['brand_ar']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($car['model_ar']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo $car['year']; ?></span>
                                    </td>
                                    <td>
                                        <strong class="text-primary"><?php echo number_format($car['price']); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo $car['currency']; ?></small>
                                    </td>
                                    <td>
                                        <?php echo number_format($car['mileage']); ?> كم
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo htmlspecialchars($car['category_ar'] ?? 'غير محدد'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($car['is_sold']): ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-handshake me-1"></i>مباع
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check-circle me-1"></i>متاح
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo formatDateArabic($car['created_at']); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?php echo $car['car_id']; ?>"
                                                class="btn btn-outline-info"
                                                data-bs-toggle="tooltip"
                                                title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $car['car_id']; ?>"
                                                class="btn btn-outline-warning"
                                                data-bs-toggle="tooltip"
                                                title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="manage_images.php?id=<?php echo $car['car_id']; ?>"
                                                class="btn btn-outline-secondary"
                                                data-bs-toggle="tooltip"
                                                title="إدارة الصور">
                                                <i class="fas fa-images"></i>
                                            </a>
                                            <a href="manage_features.php?id=<?php echo $car['car_id']; ?>"
                                                class="btn btn-outline-primary"
                                                data-bs-toggle="tooltip"
                                                title="إدارة المميزات">
                                                <i class="fas fa-star"></i>
                                            </a>
                                            <a href="?delete=<?php echo $car['car_id']; ?>"
                                                class="btn btn-outline-danger btn-delete"
                                                data-bs-toggle="tooltip"
                                                title="حذف"
                                                data-name="<?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar']); ?>">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo generatePaginationHTML($pagination, 'index.php'); ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة عرض الصورة -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الصورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="معاينة الصورة">
            </div>
        </div>
    </div>
</div>

<?php
// إضافة JavaScript مخصص
$customScript = '
    // عرض الصورة في نافذة منبثقة
    function showImageModal(imageSrc) {
        document.getElementById("modalImage").src = imageSrc;
        var imageModal = new bootstrap.Modal(document.getElementById("imageModal"));
        imageModal.show();
    }

    // تصدير البيانات لـ Excel
    function exportToExcel() {
        var table = document.getElementById("carsTable");
        var wb = XLSX.utils.table_to_book(table, {sheet: "السيارات"});
        XLSX.writeFile(wb, "cars_" + new Date().toISOString().slice(0,10) + ".xlsx");
    }
';

include '../../includes/footer.php';
?>