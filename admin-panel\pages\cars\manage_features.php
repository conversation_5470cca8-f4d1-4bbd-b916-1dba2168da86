<?php
/**
 * صفحة إدارة مميزات السيارة
 * Car Features Management Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// عنوان الصفحة
$pageTitle = 'إدارة الميزات';

$error = '';
$success = '';
$car = null;

// التحقق من وجود ID السيارة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$carId = intval($_GET['id']);

// استرجاع بيانات السيارة
try {
    $car = $db->Fetch("SELECT * FROM cars WHERE car_id = :car_id", ['car_id' => $carId]);
    if (!$car) {
        header('Location: index.php');
        exit;
    }
} catch (Exception $e) {
    $error = 'خطأ في استرجاع بيانات السيارة: ' . $e->getMessage();
}

// معالجة الطلبات AJAX
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    header('Content-Type: application/json');

    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add_feature':
            if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
                echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
                exit;
            }

            $featureData = [
                'car_id' => $carId,
                'feature_type_ar' => sanitizeInput($_POST['feature_type_ar'] ?? ''),
                'feature_type_en' => sanitizeInput($_POST['feature_type_en'] ?? ''),
                'feature_name_ar' => sanitizeInput($_POST['feature_name_ar'] ?? ''),
                'feature_name_en' => sanitizeInput($_POST['feature_name_en'] ?? ''),
                'feature_value_ar' => sanitizeInput($_POST['feature_value_ar'] ?? ''),
                'feature_value_en' => sanitizeInput($_POST['feature_value_en'] ?? '')
            ];

            if (empty($featureData['feature_type_ar']) || empty($featureData['feature_name_ar'])) {
                echo json_encode(['success' => false, 'message' => 'نوع الميزة واسم الميزة مطلوبان']);
                exit;
            }

            try {
                $stmt = "INSERT INTO car_features (
                    car_id, feature_type_ar, feature_type_en,
                    feature_name_ar, feature_name_en,
                    feature_value_ar, feature_value_en
                ) VALUES (
                    :car_id, :feature_type_ar, :feature_type_en,
                    :feature_name_ar, :feature_name_en,
                    :feature_value_ar, :feature_value_en
                )";

                $featureId = $db->Insert($stmt, $featureData);

                echo json_encode([
                    'success' => true, 
                    'message' => 'تم إضافة الميزة بنجاح',
                    'feature_id' => $featureId,
                    'feature' => $featureData
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'خطأ في إضافة الميزة: ' . $e->getMessage()]);
            }
            exit;

        case 'update_feature':
            if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
                echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
                exit;
            }

            $featureId = intval($_POST['feature_id'] ?? 0);
            if ($featureId <= 0) {
                echo json_encode(['success' => false, 'message' => 'معرف الميزة غير صحيح']);
                exit;
            }

            $featureData = [
                'feature_type_ar' => sanitizeInput($_POST['feature_type_ar'] ?? ''),
                'feature_type_en' => sanitizeInput($_POST['feature_type_en'] ?? ''),
                'feature_name_ar' => sanitizeInput($_POST['feature_name_ar'] ?? ''),
                'feature_name_en' => sanitizeInput($_POST['feature_name_en'] ?? ''),
                'feature_value_ar' => sanitizeInput($_POST['feature_value_ar'] ?? ''),
                'feature_value_en' => sanitizeInput($_POST['feature_value_en'] ?? ''),
                'feature_id' => $featureId,
                'car_id' => $carId
            ];

            if (empty($featureData['feature_type_ar']) || empty($featureData['feature_name_ar'])) {
                echo json_encode(['success' => false, 'message' => 'نوع الميزة واسم الميزة مطلوبان']);
                exit;
            }

            try {
                $stmt = "UPDATE car_features SET 
                    feature_type_ar = :feature_type_ar,
                    feature_type_en = :feature_type_en,
                    feature_name_ar = :feature_name_ar,
                    feature_name_en = :feature_name_en,
                    feature_value_ar = :feature_value_ar,
                    feature_value_en = :feature_value_en
                    WHERE feature_id = :feature_id AND car_id = :car_id";

                $db->Update($stmt, $featureData);

                echo json_encode(['success' => true, 'message' => 'تم تحديث الميزة بنجاح']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'خطأ في تحديث الميزة: ' . $e->getMessage()]);
            }
            exit;

        case 'delete_feature':
            if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
                echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
                exit;
            }

            $featureId = intval($_POST['feature_id'] ?? 0);
            if ($featureId > 0) {
                try {
                    $db->Remove("DELETE FROM car_features WHERE feature_id = :feature_id AND car_id = :car_id", [
                        'feature_id' => $featureId,
                        'car_id' => $carId
                    ]);

                    echo json_encode(['success' => true, 'message' => 'تم حذف الميزة بنجاح']);
                } catch (Exception $e) {
                    echo json_encode(['success' => false, 'message' => 'خطأ في حذف الميزة: ' . $e->getMessage()]);
                }
            } else {
                echo json_encode(['success' => false, 'message' => 'معرف الميزة غير صحيح']);
            }
            exit;

        default:
            echo json_encode(['success' => false, 'message' => 'عملية غير مدعومة']);
            exit;
    }
}

// استرجاع مميزات السيارة
$features = [];
if ($car) {
    $features = $db->FetchAll("SELECT * FROM car_features WHERE car_id = :car_id ORDER BY feature_type_ar, feature_name_ar", ['car_id' => $carId]);
}

include '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة مميزات السيارة</h1>
            <p class="text-muted">
                <?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar'] . ' - ' . $car['year']); ?>
            </p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $carId; ?>" class="btn btn-outline-primary">
                <i class="fas fa-eye me-2"></i>
                عرض السيارة
            </a>
            <a href="edit.php?id=<?php echo $carId; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-edit me-2"></i>
                تعديل السيارة
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <div id="alertContainer"></div>

    <div class="row">
        <!-- قسم إضافة ميزة جديدة -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة ميزة جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form id="addFeatureForm">
                        <?php echo getCSRFTokenField(); ?>
                        <input type="hidden" name="action" value="add_feature">

                        <div class="mb-3">
                            <label class="form-label">نوع الميزة (عربي) <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   name="feature_type_ar" 
                                   placeholder="مثال: الأمان"
                                   required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">نوع الميزة (إنجليزي)</label>
                            <input type="text" 
                                   class="form-control" 
                                   name="feature_type_en" 
                                   placeholder="Example: Safety">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">اسم الميزة (عربي) <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   name="feature_name_ar" 
                                   placeholder="مثال: وسائد هوائية"
                                   required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">اسم الميزة (إنجليزي)</label>
                            <input type="text" 
                                   class="form-control" 
                                   name="feature_name_en" 
                                   placeholder="Example: Airbags">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">قيمة الميزة (عربي)</label>
                            <input type="text" 
                                   class="form-control" 
                                   name="feature_value_ar" 
                                   placeholder="مثال: 6 وسائد">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">قيمة الميزة (إنجليزي)</label>
                            <input type="text" 
                                   class="form-control" 
                                   name="feature_value_en" 
                                   placeholder="Example: 6 airbags">
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة الميزة
                        </button>
                    </form>
                </div>
            </div>

            <!-- قائمة أنواع الميزات الشائعة -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        أنواع ميزات شائعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm feature-type-btn" data-type="الأمان">الأمان</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm feature-type-btn" data-type="الراحة">الراحة</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm feature-type-btn" data-type="التكنولوجيا">التكنولوجيا</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm feature-type-btn" data-type="الترفيه">الترفيه</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm feature-type-btn" data-type="الخارجية">الخارجية</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm feature-type-btn" data-type="الداخلية">الداخلية</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm feature-type-btn" data-type="الأداء">الأداء</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم إدارة الميزات -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star me-2"></i>
                        مميزات السيارة (<?php echo count($features); ?>)
                    </h5>
                    <?php if (count($features) > 0): ?>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteAllFeatures()">
                            <i class="fas fa-trash me-1"></i>
                            حذف الكل
                        </button>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (count($features) > 0): ?>
                        <div id="featuresContainer">
                            <?php 
                            $groupedFeatures = [];
                            foreach ($features as $feature) {
                                $type = $feature['feature_type_ar'] ?: 'أخرى';
                                if (!isset($groupedFeatures[$type])) {
                                    $groupedFeatures[$type] = [];
                                }
                                $groupedFeatures[$type][] = $feature;
                            }
                            ?>

                            <?php foreach ($groupedFeatures as $type => $typeFeatures): ?>
                                <div class="feature-group mb-4">
                                    <h6 class="border-bottom pb-2 mb-3">
                                        <i class="fas fa-tag me-2"></i>
                                        <?php echo htmlspecialchars($type); ?>
                                        <span class="badge bg-secondary ms-2"><?php echo count($typeFeatures); ?></span>
                                    </h6>

                                    <div class="row">
                                        <?php foreach ($typeFeatures as $feature): ?>
                                            <div class="col-md-6 mb-3" data-feature-id="<?php echo $feature['feature_id']; ?>">
                                                <div class="card feature-card">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div class="feature-content flex-grow-1">
                                                                <h6 class="card-title mb-1">
                                                                    <?php echo htmlspecialchars($feature['feature_name_ar']); ?>
                                                                    <?php if ($feature['feature_name_en']): ?>
                                                                        <small class="text-muted">
                                                                            (<?php echo htmlspecialchars($feature['feature_name_en']); ?>)
                                                                        </small>
                                                                    <?php endif; ?>
                                                                </h6>

                                                                <?php if ($feature['feature_value_ar']): ?>
                                                                    <p class="card-text text-muted mb-0">
                                                                        <i class="fas fa-info-circle me-1"></i>
                                                                        <?php echo htmlspecialchars($feature['feature_value_ar']); ?>
                                                                        <?php if ($feature['feature_value_en']): ?>
                                                                            <br><small><?php echo htmlspecialchars($feature['feature_value_en']); ?></small>
                                                                        <?php endif; ?>
                                                                    </p>
                                                                <?php endif; ?>
                                                            </div>

                                                            <div class="feature-actions">
                                                                <div class="dropdown">
                                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                                            type="button" 
                                                                            data-bs-toggle="dropdown">
                                                                        <i class="fas fa-ellipsis-v"></i>
                                                                    </button>
                                                                    <ul class="dropdown-menu">
                                                                        <li>
                                                                            <a class="dropdown-item" 
                                                                               href="#" 
                                                                               onclick="editFeature(<?php echo $feature['feature_id']; ?>)">
                                                                                <i class="fas fa-edit me-2"></i>
                                                                                تعديل
                                                                            </a>
                                                                        </li>
                                                                        <li>
                                                                            <a class="dropdown-item text-danger" 
                                                                               href="#" 
                                                                               onclick="deleteFeature(<?php echo $feature['feature_id']; ?>)">
                                                                                <i class="fas fa-trash me-2"></i>
                                                                                حذف
                                                                            </a>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مميزات مضافة</h5>
                            <p class="text-muted">ابدأ بإضافة أول ميزة للسيارة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل الميزة -->
<div class="modal fade" id="editFeatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الميزة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editFeatureForm">
                <div class="modal-body">
                    <?php echo getCSRFTokenField(); ?>
                    <input type="hidden" name="action" value="update_feature">
                    <input type="hidden" name="feature_id" id="editFeatureId">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الميزة (عربي) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="feature_type_ar" id="editFeatureTypeAr" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نوع الميزة (إنجليزي)</label>
                            <input type="text" class="form-control" name="feature_type_en" id="editFeatureTypeEn">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الميزة (عربي) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="feature_name_ar" id="editFeatureNameAr" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الميزة (إنجليزي)</label>
                            <input type="text" class="form-control" name="feature_name_en" id="editFeatureNameEn">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">قيمة الميزة (عربي)</label>
                            <input type="text" class="form-control" name="feature_value_ar" id="editFeatureValueAr">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">قيمة الميزة (إنجليزي)</label>
                            <input type="text" class="form-control" name="feature_value_en" id="editFeatureValueEn">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إضافة ميزة جديدة
document.getElementById('addFeatureForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            this.reset();
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الشبكة');
    });
});

// تعديل ميزة
function editFeature(featureId) {
    // استرجاع بيانات الميزة من DOM
    const featureCard = document.querySelector(`[data-feature-id="${featureId}"]`);
    if (!featureCard) return;

    // هنا يمكن إضافة كود لاسترجاع البيانات من الخادم
    // أو يمكن تخزين البيانات في data attributes

    document.getElementById('editFeatureId').value = featureId;

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('editFeatureModal'));
    modal.show();
}

// تحديث ميزة
document.getElementById('editFeatureForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('editFeatureModal')).hide();
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الشبكة');
    });
});

// حذف ميزة
function deleteFeature(featureId) {
    if (!confirm('هل أنت متأكد من حذف هذه الميزة؟')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'delete_feature');
    formData.append('feature_id', featureId);
    formData.append('csrf_token', document.querySelector('[name="csrf_token"]').value);

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            document.querySelector(`[data-feature-id="${featureId}"]`).remove();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'حدث خطأ في الشبكة');
    });
}

// حذف جميع الميزات
function deleteAllFeatures() {
    if (!confirm('هل أنت متأكد من حذف جميع المميزات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    // يمكن إضافة هذه الوظيفة لاحقاً
    showAlert('info', 'هذه الوظيفة ستكون متاحة قريباً');
}

// أزرار أنواع الميزات السريعة
document.querySelectorAll('.feature-type-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const type = this.dataset.type;
        document.querySelector('[name="feature_type_ar"]').value = type;
    });
});

// عرض التنبيهات
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.getElementById('alertContainer').innerHTML = alertHtml;
}
</script>

<?php include '../../includes/footer.php'; ?>