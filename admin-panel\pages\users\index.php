<?php
/**
 * صفحة إدارة المستخدمين
 * Users Management Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من صلاحيات الإدارة
checkAdminPrivileges();

// عنوان الصفحة
$pageTitle = 'إدارة المستخدمين';

$error = '';
$success = '';

// معالجة إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        $fullname = sanitizeInput($_POST['fullname'] ?? '');
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $isAdmin = isset($_POST['is_admin']) ? 1 : 0;

        // التحقق من البيانات
        if (empty($fullname) || empty($username) || empty($password)) {
            $error = 'جميع الحقول مطلوبة';
        } elseif (strlen($username) < 3) {
            $error = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
        } elseif (strlen($password) < 6) {
            $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } elseif ($password !== $confirmPassword) {
            $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتان';
        } else {
            try {
                // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
                $existingUser = $db->Fetch("SELECT id FROM users WHERE username = :username", 
                    ['username' => $username]);

                if ($existingUser) {
                    $error = 'اسم المستخدم موجود بالفعل';
                } else {
                    $hashedPassword = hashPassword($password);

                    $stmt = "INSERT INTO users (fullname, username, password, is_admin, created_at, update_at) 
                             VALUES (:fullname, :username, :password, :is_admin, NOW(), NOW())";
                    $db->Insert($stmt, [
                        'fullname' => $fullname,
                        'username' => $username,
                        'password' => $hashedPassword,
                        'is_admin' => $isAdmin
                    ]);

                    $_SESSION['success_message'] = 'تم إضافة المستخدم بنجاح';
                    header('Location: index.php');
                    exit();
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء إضافة المستخدم: ' . $e->getMessage();
            }
        }
    }
}

// معالجة تعديل المستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'edit') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        jsonResponse(false, 'رمز الأمان غير صحيح');
    }

    $userId = intval($_POST['user_id'] ?? 0);
    $fullname = sanitizeInput($_POST['fullname'] ?? '');
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $isAdmin = isset($_POST['is_admin']) ? 1 : 0;

    if ($userId <= 0 || empty($fullname) || empty($username)) {
        jsonResponse(false, 'البيانات غير صحيحة');
    }

    // منع المستخدم من إزالة صلاحيات الإدارة من نفسه
    if ($userId == $_SESSION['user_id'] && $isAdmin == 0) {
        jsonResponse(false, 'لا يمكنك إزالة صلاحيات الإدارة من حسابك');
    }

    try {
        // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
        $existingUser = $db->Fetch("SELECT id FROM users WHERE username = :username AND id != :user_id", 
            ['username' => $username, 'user_id' => $userId]);

        if ($existingUser) {
            jsonResponse(false, 'اسم المستخدم موجود بالفعل');
        }

        // تحديث البيانات
        if (!empty($password)) {
            // تحديث مع كلمة المرور
            if (strlen($password) < 6) {
                jsonResponse(false, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }

            $hashedPassword = hashPassword($password);
            $stmt = "UPDATE users SET fullname = :fullname, username = :username, password = :password, 
                     is_admin = :is_admin, update_at = NOW() WHERE id = :user_id";
            $params = [
                'user_id' => $userId,
                'fullname' => $fullname,
                'username' => $username,
                'password' => $hashedPassword,
                'is_admin' => $isAdmin
            ];
        } else {
            // تحديث بدون كلمة المرور
            $stmt = "UPDATE users SET fullname = :fullname, username = :username, 
                     is_admin = :is_admin, update_at = NOW() WHERE id = :user_id";
            $params = [
                'user_id' => $userId,
                'fullname' => $fullname,
                'username' => $username,
                'is_admin' => $isAdmin
            ];
        }

        // تسجيل الاستعلام للتشخيص
        error_log("Update query: " . $stmt);
        error_log("Update params: " . json_encode($params));

        $db->Update($stmt, $params);

        // التحقق من أن المستخدم موجود بعد التحديث
        $updatedUser = $db->Fetch("SELECT id FROM users WHERE id = :user_id", ['user_id' => $userId]);

        if ($updatedUser) {
            jsonResponse(true, 'تم تحديث المستخدم بنجاح');
        } else {
            error_log("Failed to update user ID: $userId - User not found");
            jsonResponse(false, 'فشل في تحديث المستخدم - المستخدم غير موجود');
        }
    } catch (Exception $e) {
        error_log("Error updating user ID $userId: " . $e->getMessage());
        jsonResponse(false, 'حدث خطأ أثناء تحديث المستخدم: ' . $e->getMessage());
    }
}

// معالجة حذف المستخدم
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $userId = intval($_GET['delete']);

    // منع حذف المستخدم الحالي
    if ($userId == $_SESSION['user_id']) {
        $_SESSION['error_message'] = 'لا يمكنك حذف حسابك الخاص';
        header('Location: index.php');
        exit();
    }

    try {
        $db->Remove("DELETE FROM users WHERE id = :user_id", ['user_id' => $userId]);
        $_SESSION['success_message'] = 'تم حذف المستخدم بنجاح';

        header('Location: index.php');
        exit();

    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage();
        header('Location: index.php');
        exit();
    }
}

try {
    // استرجاع جميع المستخدمين
    $users = $db->FetchAll("SELECT * FROM users ORDER BY created_at DESC");
} catch (Exception $e) {
    $error = "خطأ في استرجاع البيانات: " . $e->getMessage();
    $users = [];
}

// تضمين ملف الرأس
include '../../includes/header.php';
?>

<!-- محتوى صفحة إدارة المستخدمين -->
<div class="container-fluid p-4">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة المستخدمين</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../../dashboard.php">الرئيسية</a></li>
                    <li class="breadcrumb-item active">إدارة المستخدمين</li>
                </ol>
            </nav>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-user-plus me-2"></i>
            إضافة مستخدم جديد
        </button>
    </div>

    <!-- رسائل الأخطاء والنجاح -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary mx-auto mb-2">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number"><?php echo count($users); ?></div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success mx-auto mb-2">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="stat-number"><?php echo count(array_filter($users, function($u) { return $u['is_admin'] == 1; })); ?></div>
                    <div class="stat-label">المدراء</div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info mx-auto mb-2">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="stat-number"><?php echo count(array_filter($users, function($u) { return $u['is_admin'] == 0; })); ?></div>
                    <div class="stat-label">المستخدمين العاديين</div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المستخدمين -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-users me-2"></i>
                المستخدمين (<?php echo count($users); ?> مستخدم)
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($users)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">لا يوجد مستخدمين</h4>
                    <p class="text-muted">ابدأ بإضافة مستخدم جديد</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover data-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                                <th>النوع</th>
                                <th>تاريخ الإنشاء</th>
                                <th>آخر تحديث</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            <?php echo strtoupper(substr($user['fullname'], 0, 1)); ?>
                                        </div>
                                        <strong><?php echo htmlspecialchars($user['fullname']); ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($user['username']); ?></code>
                                    <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                        <span class="badge bg-primary ms-1">أنت</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user['is_admin']): ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-shield-alt me-1"></i>مدير
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-user me-1"></i>مستخدم
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo formatDateArabic($user['created_at']); ?></small>
                                </td>
                                <td>
                                    <small><?php echo formatDateArabic($user['update_at']); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" 
                                                class="btn btn-outline-warning edit-user"
                                                data-id="<?php echo $user['id']; ?>"
                                                data-fullname="<?php echo htmlspecialchars($user['fullname']); ?>"
                                                data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                                data-is-admin="<?php echo $user['is_admin']; ?>"
                                                data-bs-toggle="tooltip" 
                                                title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                        <a href="index.php?delete=<?php echo $user['id']; ?>" 
                                           class="btn btn-outline-danger btn-delete"
                                           data-name="<?php echo htmlspecialchars($user['fullname']); ?>"
                                           data-bs-toggle="tooltip" 
                                           title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        <?php else: ?>
                                        <button type="button" 
                                                class="btn btn-outline-secondary"
                                                data-bs-toggle="tooltip" 
                                                title="لا يمكن حذف حسابك"
                                                disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="addUserForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="add">

                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="fullname" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="username" required>
                        <div class="form-text">يجب أن يكون 3 أحرف على الأقل</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" name="password" required>
                        <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" name="confirm_password" required>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_admin" id="is_admin">
                        <label class="form-check-label" for="is_admin">
                            <strong>مدير النظام</strong>
                            <br><small class="text-muted">يمكن للمدير الوصول لجميع أقسام النظام</small>
                        </label>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> إضافة المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل المستخدم -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="editUserForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="user_id" id="edit_user_id">

                <div class="modal-header">
                    <h5 class="modal-title">تعديل المستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="fullname" id="edit_fullname" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="username" id="edit_username" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" name="password" id="edit_password">
                        <div class="form-text">اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور</div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_admin" id="edit_is_admin">
                        <label class="form-check-label" for="edit_is_admin">
                            <strong>مدير النظام</strong>
                        </label>
                        <div id="admin-warning" class="alert alert-warning mt-2 d-none">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            لا يمكنك إزالة صلاحيات الإدارة من حسابك الخاص
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i> حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// إضافة JavaScript مخصص
$customScript = <<<'EOD'
// تعديل المستخدم
$('.edit-user').on('click', function() {
    const userId = $(this).data('id');
    const fullname = $(this).data('fullname');
    const username = $(this).data('username');
    const isAdmin = $(this).data('is-admin');

    // تنظيف النموذج
    $('#editUserForm')[0].reset();
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').remove();
    $('#admin-warning').addClass('d-none');

    // ملء البيانات
    $('#edit_user_id').val(userId);
    $('#edit_fullname').val(fullname);
    $('#edit_username').val(username);
    $('#edit_password').val('');
    $('#edit_is_admin').prop('checked', isAdmin == 1);

    // إظهار تحذير إذا كان المستخدم الحالي
    if (userId == " . $_SESSION['user_id'] . ") {
        $('#edit_is_admin').off('change').on('change', function() {
            if (!$(this).is(':checked')) {
                $('#admin-warning').removeClass('d-none');
                $(this).prop('checked', true);
            } else {
                $('#admin-warning').addClass('d-none');
            }
        });
    } else {
        $('#edit_is_admin').off('change');
    }

    $('#editUserModal').modal('show');
});

// تنظيف النموذج عند إغلاق النافذة
$('#editUserModal').on('hidden.bs.modal', function() {
    $('#editUserForm')[0].reset();
    $('.is-invalid').removeClass('is-invalid');
    $('.invalid-feedback').remove();
    $('#admin-warning').addClass('d-none');
    $('#edit_is_admin').off('change');
});

// إرسال نموذج التعديل
$('#editUserForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    $.ajax({
        url: 'index.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        processData: false,
        contentType: false,
        beforeSend: function() {
            // إظهار مؤشر التحميل
            Swal.fire({
                title: 'جاري التحديث...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        },
        success: function(response) {
            Swal.close();
            if (response.success) {
                $('#editUserModal').modal('hide');
                Swal.fire({
                    title: 'تم بنجاح!',
                    text: response.message,
                    icon: 'success'
                }).then(() => {
                    location.reload();
                });
            } else {
                Swal.fire({
                    title: 'خطأ!',
                    text: response.message,
                    icon: 'error'
                });
            }
        },
        error: function(xhr, status, error) {
            Swal.close();
            console.log('AJAX Error:', xhr.responseText);
            Swal.fire({
                title: 'خطأ في الاتصال!',
                text: 'حدث خطأ أثناء تحديث المستخدم. يرجى المحاولة مرة أخرى.',
                icon: 'error'
            });
        }
    });
});

// التحقق من تطابق كلمة المرور
$('#addUserForm input[name="confirm_password"]').on('keyup', function() {
    const password = $('input[name="password"]').val();
    const confirmPassword = $(this).val();

    if (password !== confirmPassword) {
        $(this).addClass('is-invalid');
        $(this).next('.invalid-feedback').remove();
        $(this).after('<div class="invalid-feedback">كلمة المرور غير متطابقة</div>');
    } else {
        $(this).removeClass('is-invalid');
        $(this).next('.invalid-feedback').remove();
    }
});
EOD;

include '../../includes/footer.php';
?>