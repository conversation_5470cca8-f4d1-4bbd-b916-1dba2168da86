<?php
// تضمين ملفات النظام المطلوبة
require_once 'includes/config.php';
require_once 'includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// إعداد عنوان الصفحة
$pageTitle = 'الإحصائيات والتحليلات';

// Get selected period
$period = $_GET['period'] ?? 'today';
$valid_periods = ['today', 'week', 'month', 'year', 'all'];
if (!in_array($period, $valid_periods)) {
    $period = 'today';
}

// Get analytics data
$analytics = getAnalyticsData($period);
$chart_data = getVisitsChartData(30);
$real_time_visitors = getRealTimeVisitors();

// Period labels
$period_labels = [
    'today' => 'اليوم',
    'week' => 'هذا الأسبوع',
    'month' => 'هذا الشهر',
    'year' => 'هذا العام',
    'all' => 'الإجمالي'
];

include 'includes/header.php';
?>

<!-- محتوى صفحة الإحصائيات -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-line me-2"></i>
        الإحصائيات والتحليلات
    </h1>

    <!-- Period Selector -->
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <?php foreach ($period_labels as $key => $label): ?>
                <a href="?period=<?php echo $key; ?>"
                    class="btn btn-sm <?php echo $period === $key ? 'btn-primary' : 'btn-outline-secondary'; ?>">
                    <?php echo $label; ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- Real-time Stats -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info d-flex align-items-center">
            <i class="fas fa-users fa-2x me-3"></i>
            <div>
                <h5 class="mb-1">الزوار النشطون الآن</h5>
                <h3 class="mb-0 text-primary"><?php echo number_format($real_time_visitors); ?> زائر</h3>
                <small class="text-muted">خلال آخر 5 دقائق</small>
            </div>
        </div>
    </div>
</div>

<!-- Main Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الزيارات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($analytics['stats']['total_visits'] ?? 0); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            الزوار الفريدون
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($analytics['stats']['unique_visitors'] ?? 0); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            الجلسات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($analytics['stats']['total_sessions'] ?? 0); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            متوسط مدة الزيارة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php
                            $avg_duration = $analytics['stats']['avg_duration'] ?? 0;
                            echo gmdate("i:s", $avg_duration);
                            ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-stopwatch fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Visits Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">الزيارات خلال آخر 30 يوم</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="visitsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Stats -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">الأجهزة</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="deviceChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <?php foreach ($analytics['devices'] as $device): ?>
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i>
                            <?php echo ucfirst($device['device_type']); ?>: <?php echo $device['percentage']; ?>%
                        </span>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Pages and Cars -->
<div class="row mb-4">
    <!-- Top Pages -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الصفحات الأكثر زيارة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>الصفحة</th>
                                <th>الزيارات</th>
                                <th>زوار فريدون</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($analytics['top_pages'] as $page): ?>
                                <tr>
                                    <td>
                                        <div class="font-weight-bold">
                                            <?php echo htmlspecialchars($page['page_title'] ?: 'غير محدد'); ?>
                                        </div>
                                        <div class="small text-muted">
                                            <?php echo htmlspecialchars($page['page_url']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo number_format($page['visits']); ?></td>
                                    <td><?php echo number_format($page['unique_visitors']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Cars -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">السيارات الأكثر مشاهدة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>السيارة</th>
                                <th>المشاهدات</th>
                                <th>زوار فريدون</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($analytics['top_cars'] as $car): ?>
                                <tr>
                                    <td>
                                        <div class="font-weight-bold">
                                            <?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar']); ?>
                                        </div>
                                        <div class="small text-muted">
                                            <?php echo htmlspecialchars($car['brand_en'] . ' ' . $car['model_en']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo number_format($car['visits']); ?></td>
                                    <td><?php echo number_format($car['unique_visitors']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Browser Stats -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">المتصفحات الأكثر استخداماً</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($analytics['browsers'] as $browser): ?>
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-primary">
                                    <?php echo $browser['percentage']; ?>%
                                </div>
                                <div class="small">
                                    <?php echo htmlspecialchars($browser['browser']); ?>
                                </div>
                                <div class="small text-muted">
                                    <?php echo number_format($browser['count']); ?> زيارة
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Visits Chart
    const visitsCtx = document.getElementById('visitsChart').getContext('2d');
    const visitsChart = new Chart(visitsCtx, {
        type: 'line',
        data: {
            labels: [
                <?php
                foreach ($chart_data as $data) {
                    echo "'" . date('M d', strtotime($data['date'])) . "',";
                }
                ?>
            ],
            datasets: [{
                label: 'إجمالي الزيارات',
                data: [<?php foreach ($chart_data as $data) echo $data['visits'] . ','; ?>],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }, {
                label: 'الزوار الفريدون',
                data: [<?php foreach ($chart_data as $data) echo $data['unique_visitors'] . ','; ?>],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Device Chart
    const deviceCtx = document.getElementById('deviceChart').getContext('2d');
    const deviceChart = new Chart(deviceCtx, {
        type: 'doughnut',
        data: {
            labels: [
                <?php foreach ($analytics['devices'] as $device) echo "'" . ucfirst($device['device_type']) . "',"; ?>
            ],
            datasets: [{
                data: [
                    <?php foreach ($analytics['devices'] as $device) echo $device['count'] . ','; ?>
                ],
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a',
                    '#36b9cc',
                    '#f6c23e',
                    '#e74a3b'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });

    // Auto refresh real-time stats every 30 seconds
    setInterval(function() {
        fetch('ajax/get_realtime_stats.php')
            .then(response => response.json())
            .then(data => {
                if (data.active_visitors !== undefined) {
                    document.querySelector('.alert-info h3').textContent =
                        new Intl.NumberFormat('ar-EG').format(data.active_visitors) + ' زائر';
                }
            })
            .catch(error => console.log('Error fetching real-time stats:', error));
    }, 30000);
</script>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }

    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }

    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }

    .text-xs {
        font-size: 0.7rem;
    }

    .chart-area {
        position: relative;
        height: 320px;
    }

    .chart-pie {
        position: relative;
        height: 245px;
    }
</style>

<?php include 'includes/footer.php'; ?>