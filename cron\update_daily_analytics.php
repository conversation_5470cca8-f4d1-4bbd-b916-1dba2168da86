<?php

/**
 * Daily Analytics Update Cron Job
 * Run this script daily to update analytics summary
 * 
 * Add to crontab:
 * 0 1 * * * /usr/bin/php /path/to/your/site/cron/update_daily_analytics.php
 */

// Set execution time limit
set_time_limit(300); // 5 minutes

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Log file for cron job
$log_file = '../../logs/analytics_cron.log';

function logMessage($message)
{
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;

    // Create logs directory if it doesn't exist
    $log_dir = dirname($log_file);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }

    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

try {
    logMessage("Starting daily analytics update...");

    // Update yesterday's analytics summary
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    
    // Custom function to update daily summary for specific date
    updateDailyAnalyticsSummaryForDate($yesterday);

    logMessage("Successfully updated analytics summary for $yesterday");

    // Clean up old analytics data (older than 2 years)
    $cutoff_date = date('Y-m-d', strtotime('-2 years'));

    // First count how many records will be deleted
    $count_sql = "SELECT COUNT(*) as count FROM site_analytics WHERE DATE(created_at) < ?";
    $count_result = $db->Fetch($count_sql, [$cutoff_date]);
    $records_to_delete = $count_result['count'] ?? 0;

    // Delete old records if any exist
    if ($records_to_delete > 0) {
        $cleanup_sql = "DELETE FROM site_analytics WHERE DATE(created_at) < ?";
        $db->Remove($cleanup_sql, [$cutoff_date]);
        logMessage("Cleaned up $records_to_delete old analytics records (older than $cutoff_date)");
    } else {
        logMessage("No old analytics records to clean up (older than $cutoff_date)");
    }

    // Update analytics summary for the last 7 days (in case of missed updates)
    for ($i = 2; $i <= 7; $i++) {
        $date = date('Y-m-d', strtotime("-$i days"));
        updateDailyAnalyticsSummaryForDate($date);
    }

    logMessage("Updated analytics summaries for the last 7 days");

    // Generate weekly and monthly reports (optional)
    generateWeeklyReport();
    generateMonthlyReport();

    logMessage("Daily analytics update completed successfully");
    
} catch (Exception $e) {
    logMessage("Error during analytics update: " . $e->getMessage());
    logMessage("Stack trace: " . $e->getTraceAsString());

    // Send email notification on error (optional)
    if (defined('ADMIN_EMAIL') && ADMIN_EMAIL) {
        $subject = "Analytics Cron Job Error - " . date('Y-m-d');
        $message = "An error occurred during the daily analytics update:\n\n" . $e->getMessage() . "\n\nStack trace:\n" . $e->getTraceAsString();
        mail(ADMIN_EMAIL, $subject, $message);
    }
}

/**
 * Update daily analytics summary for specific date
 */
function updateDailyAnalyticsSummaryForDate($date)
{
    global $db;

    try {
        // Get analytics data for the specific date
        $stats_sql = "SELECT 
                        COUNT(*) as total_visits,
                        COUNT(DISTINCT visitor_ip) as unique_visitors,
                        COUNT(DISTINCT session_id) as total_sessions,
                        AVG(visit_duration) as avg_duration
                      FROM site_analytics 
                      WHERE DATE(created_at) = ?";

        $stats = $db->Fetch($stats_sql, [$date]);

        // Get top pages for the date
        $pages_sql = "SELECT 
                        page_url,
                        page_title,
                        COUNT(*) as visits,
                        COUNT(DISTINCT visitor_ip) as unique_visitors
                      FROM site_analytics 
                      WHERE DATE(created_at) = ?
                      GROUP BY page_url, page_title
                      ORDER BY visits DESC
                      LIMIT 5";

        $top_pages = $db->FetchAll($pages_sql, [$date]);

        // Get top cars for the date
        $cars_sql = "SELECT 
                        sa.car_id,
                        c.brand_ar,
                        c.brand_en,
                        c.model_ar,
                        c.model_en,
                        COUNT(*) as visits,
                        COUNT(DISTINCT sa.visitor_ip) as unique_visitors
                     FROM site_analytics sa
                     LEFT JOIN cars c ON sa.car_id = c.car_id
                     WHERE DATE(sa.created_at) = ? AND sa.car_id IS NOT NULL
                     GROUP BY sa.car_id
                     ORDER BY visits DESC
                     LIMIT 5";

        $top_cars = $db->FetchAll($cars_sql, [$date]);

        // Prepare data for insertion
        $top_pages_json = json_encode($top_pages);
        $top_cars_json = json_encode($top_cars);

        // Insert or update daily summary
        $sql = "INSERT INTO daily_analytics_summary 
                (date, total_visits, unique_visitors, page_views, top_pages, top_cars)
                VALUES (?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                total_visits = VALUES(total_visits),
                unique_visitors = VALUES(unique_visitors),
                page_views = VALUES(page_views),
                top_pages = VALUES(top_pages),
                top_cars = VALUES(top_cars)";

        $db->Insert($sql, [
            $date,
            $stats['total_visits'] ?? 0,
            $stats['unique_visitors'] ?? 0,
            $stats['total_visits'] ?? 0, // page_views same as visits for now
            $top_pages_json,
            $top_cars_json
        ]);

        logMessage("Updated summary for $date: {$stats['total_visits']} visits, {$stats['unique_visitors']} unique visitors");

    } catch (Exception $e) {
        logMessage("Error updating summary for $date: " . $e->getMessage());
    }
}

/**
 * Generate weekly analytics report
 */
function generateWeeklyReport()
{
    global $db;

    try {
        $start_date = date('Y-m-d', strtotime('-7 days'));
        $end_date = date('Y-m-d', strtotime('-1 day'));

        $sql = "SELECT 
                    SUM(total_visits) as weekly_visits,
                    SUM(unique_visitors) as weekly_unique,
                    AVG(avg_session_duration) as avg_duration
                FROM daily_analytics_summary 
                WHERE date BETWEEN ? AND ?";

        $weekly_stats = $db->Fetch($sql, [$start_date, $end_date]);

        // Store weekly summary (you can create a weekly_analytics_summary table)
        logMessage("Weekly stats ($start_date to $end_date): " . json_encode($weekly_stats));
        
    } catch (Exception $e) {
        logMessage("Error generating weekly report: " . $e->getMessage());
    }
}

/**
 * Generate monthly analytics report
 */
function generateMonthlyReport()
{
    global $db;

    try {
        $start_date = date('Y-m-01', strtotime('-1 month'));
        $end_date = date('Y-m-t', strtotime('-1 month'));

        $sql = "SELECT 
                    SUM(total_visits) as monthly_visits,
                    SUM(unique_visitors) as monthly_unique,
                    AVG(avg_session_duration) as avg_duration
                FROM daily_analytics_summary 
                WHERE date BETWEEN ? AND ?";

        $monthly_stats = $db->Fetch($sql, [$start_date, $end_date]);

        // Store monthly summary (you can create a monthly_analytics_summary table)
        logMessage("Monthly stats for " . date('Y-m', strtotime('-1 month')) . " ($start_date to $end_date): " . json_encode($monthly_stats));
        
    } catch (Exception $e) {
        logMessage("Error generating monthly report: " . $e->getMessage());
    }
}

// If running from command line, output the log
if (php_sapi_name() === 'cli') {
    echo "Analytics update completed. Check log file: $log_file\n";
}