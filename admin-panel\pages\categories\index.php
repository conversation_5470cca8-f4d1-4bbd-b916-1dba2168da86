<?php
/**
 * صفحة إدارة فئات السيارات
 * Car Categories Management Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// عنوان الصفحة
$pageTitle = 'إدارة فئات السيارات';

$error = '';
$success = '';

// معالجة إضافة فئة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        $categoryAr = sanitizeInput($_POST['category_ar'] ?? '');
        $categoryEn = sanitizeInput($_POST['category_en'] ?? '');
        $icon = trim($_POST['icon'] ?? ''); // لا نستخدم sanitizeInput للأيقونة للحفاظ على HTML

        if (empty($categoryAr)) {
            $error = 'اسم الفئة بالعربية مطلوب';
        } else {
            try {
                // التحقق من عدم وجود فئة بنفس الاسم
                $existingCategory = $db->Fetch("SELECT category_id FROM car_categories WHERE category_ar = :category_ar", 
                    ['category_ar' => $categoryAr]);

                if ($existingCategory) {
                    $error = 'فئة بهذا الاسم موجودة بالفعل';
                } else {
                    $stmt = "INSERT INTO car_categories (category_ar, category_en, icon) VALUES (:category_ar, :category_en, :icon)";
                    $db->Insert($stmt, [
                        'category_ar' => $categoryAr,
                        'category_en' => $categoryEn,
                        'icon' => $icon
                    ]);

                    $_SESSION['success_message'] = 'تم إضافة الفئة بنجاح';
                    header('Location: index.php');
                    exit();
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء إضافة الفئة: ' . $e->getMessage();
            }
        }
    }
}

// معالجة تعديل فئة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'edit') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        jsonResponse(false, 'رمز الأمان غير صحيح');
    }

    $categoryId = intval($_POST['category_id'] ?? 0);
    $categoryAr = sanitizeInput($_POST['category_ar'] ?? '');
    $categoryEn = sanitizeInput($_POST['category_en'] ?? '');
    $icon = trim($_POST['icon'] ?? ''); // لا نستخدم sanitizeInput للأيقونة للحفاظ على HTML

    if ($categoryId <= 0 || empty($categoryAr)) {
        jsonResponse(false, 'البيانات غير صحيحة');
    }

    try {
        $stmt = "UPDATE car_categories SET category_ar = :category_ar, category_en = :category_en, icon = :icon WHERE category_id = :category_id";
        $db->Update($stmt, [
            'category_id' => $categoryId,
            'category_ar' => $categoryAr,
            'category_en' => $categoryEn,
            'icon' => $icon
        ]);

        jsonResponse(true, 'تم تحديث الفئة بنجاح');
    } catch (Exception $e) {
        jsonResponse(false, 'حدث خطأ أثناء تحديث الفئة: ' . $e->getMessage());
    }
}

// معالجة حذف فئة
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $categoryId = intval($_GET['delete']);

    try {
        // التحقق من وجود سيارات تستخدم هذه الفئة
        $carsCount = $db->Fetch("SELECT COUNT(*) as count FROM cars WHERE category_id = :category_id", 
            ['category_id' => $categoryId])['count'];

        if ($carsCount > 0) {
            $_SESSION['error_message'] = "لا يمكن حذف هذه الفئة لأنها مستخدمة في $carsCount سيارة";
        } else {
            $db->Remove("DELETE FROM car_categories WHERE category_id = :category_id", ['category_id' => $categoryId]);
            $_SESSION['success_message'] = 'تم حذف الفئة بنجاح';
        }

        header('Location: index.php');
        exit();

    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء حذف الفئة: ' . $e->getMessage();
        header('Location: index.php');
        exit();
    }
}

try {
    // استرجاع جميع الفئات مع عدد السيارات في كل فئة
    $categories = $db->FetchAll("
        SELECT c.*, COUNT(cars.car_id) as cars_count
        FROM car_categories c
        LEFT JOIN cars ON c.category_id = cars.category_id
        GROUP BY c.category_id
        ORDER BY c.category_ar
    ");
} catch (Exception $e) {
    $error = "خطأ في استرجاع البيانات: " . $e->getMessage();
    $categories = [];
}

// تضمين ملف الرأس
include '../../includes/header.php';
?>

<!-- محتوى صفحة إدارة الفئات -->
<div class="container-fluid p-4">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة فئات السيارات</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../../dashboard.php">الرئيسية</a></li>
                    <li class="breadcrumb-item active">فئات السيارات</li>
                </ol>
            </nav>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
            <i class="fas fa-plus me-2"></i>
            إضافة فئة جديدة
        </button>
    </div>

    <!-- رسائل الأخطاء والنجاح -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- جدول الفئات -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-tags me-2"></i>
                فئات السيارات (<?php echo count($categories); ?> فئة)
            </h5>
        </div>
        <div class="card-body">
            <!-- حقل البحث -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث في الفئات...">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-outline-secondary" id="clearSearch">
                        <i class="fas fa-times me-1"></i>
                        مسح البحث
                    </button>
                </div>
            </div>

            <?php if (empty($categories)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-tags text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">لا توجد فئات</h4>
                    <p class="text-muted">ابدأ بإضافة فئة جديدة لتصنيف السيارات</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة فئة جديدة
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover table-striped align-middle" id="categoriesTable">
                        <thead class="table-dark">
                            <tr>
                                <th width="10%">
                                    <i class="fas fa-icons me-1"></i>
                                    الأيقونة
                                </th>
                                <th width="25%">
                                    <i class="fas fa-font me-1"></i>
                                    اسم الفئة (عربي)
                                </th>
                                <th width="25%">
                                    <i class="fas fa-globe me-1"></i>
                                    اسم الفئة (إنجليزي)
                                </th>
                                <th width="15%">
                                    <i class="fas fa-car me-1"></i>
                                    عدد السيارات
                                </th>
                                <th width="25%">
                                    <i class="fas fa-cogs me-1"></i>
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($categories as $category): ?>
                            <tr>
                                <td class="text-center">
                                    <?php if ($category['icon']): ?>
                                        <?php 
                                        // إذا كانت الأيقونة تحتوي على HTML كامل
                                        if (strpos($category['icon'], '<i') !== false) {
                                            echo $category['icon'];
                                        } else {
                                            // إذا كانت مجرد class
                                            echo '<i class="' . htmlspecialchars($category['icon']) . ' text-primary" style="font-size: 2rem;"></i>';
                                        }
                                        ?>
                                    <?php else: ?>
                                        <i class="fas fa-tag text-muted" style="font-size: 2rem;"></i>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <strong class="text-dark fs-6"><?php echo htmlspecialchars($category['category_ar']); ?></strong>
                                </td>
                                <td class="text-center">
                                    <span class="text-muted"><?php echo htmlspecialchars($category['category_en'] ?: '-'); ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info fs-6 px-3 py-2">
                                        <i class="fas fa-car me-1"></i>
                                        <?php echo number_format($category['cars_count']); ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" 
                                                class="btn btn-outline-warning edit-category"
                                                data-id="<?php echo $category['category_id']; ?>"
                                                data-name-ar="<?php echo htmlspecialchars($category['category_ar']); ?>"
                                                data-name-en="<?php echo htmlspecialchars($category['category_en']); ?>"
                                                data-icon="<?php echo htmlspecialchars($category['icon']); ?>"
                                                data-bs-toggle="tooltip" 
                                                title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <?php if ($category['cars_count'] == 0): ?>
                                        <a href="index.php?delete=<?php echo $category['category_id']; ?>" 
                                           class="btn btn-outline-danger btn-delete"
                                           data-name="<?php echo htmlspecialchars($category['category_ar']); ?>"
                                           data-bs-toggle="tooltip" 
                                           title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        <?php else: ?>
                                        <button type="button" 
                                                class="btn btn-outline-secondary"
                                                data-bs-toggle="tooltip" 
                                                title="لا يمكن الحذف - يحتوي على سيارات"
                                                disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="addCategoryForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="add">

                <div class="modal-header">
                    <h5 class="modal-title">إضافة فئة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الفئة (بالعربية) <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="category_ar" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم الفئة (بالإنجليزية)</label>
                        <input type="text" class="form-control" name="category_en">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">أيقونة الفئة</label>
                        <div class="input-group">
                            <input type="text" class="form-control" name="icon" id="iconInput" placeholder='<i class="fas fa-car"></i>'>
                            <button type="button" class="btn btn-outline-secondary" onclick="openIconPicker()">
                                <i class="fas fa-icons"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">
                            يمكنك اختيار أيقونة من Font Awesome أو كتابة HTML الكامل (مثل: &lt;i class="fas fa-car"&gt;&lt;/i&gt;)
                        </small>
                        <div class="mt-2">
                            <span class="text-muted">معاينة: </span>
                            <i id="iconPreview" class="fas fa-tag text-primary"></i>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الفئة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل الفئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="editCategoryForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="category_id" id="editCategoryId">

                <div class="modal-header">
                    <h5 class="modal-title">تعديل الفئة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الفئة (بالعربية) <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="category_ar" id="editCategoryAr" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم الفئة (بالإنجليزية)</label>
                        <input type="text" class="form-control" name="category_en" id="editCategoryEn">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">أيقونة الفئة</label>
                        <input type="text" class="form-control" name="icon" id="editIconInput" placeholder="fas fa-car">
                        <div class="mt-2">
                            <span class="text-muted">معاينة: </span>
                            <i id="editIconPreview" class="fas fa-tag text-primary"></i>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">تحديث الفئة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج اختيار الأيقونة -->
<div class="modal fade" id="iconPickerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختيار أيقونة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <style>
                    .icon-item {
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .icon-item:hover {
                        background-color: #f8f9fa !important;
                        border-color: #0d6efd !important;
                        transform: scale(1.05);
                    }
                </style>
                <div class="row">
                    <div class="col-12 mb-3">
                        <input type="text" class="form-control" id="iconSearch" placeholder="البحث عن أيقونة...">
                    </div>
                </div>
                <div class="row" id="iconGrid">
                    <!-- أيقونات السيارات -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-car">
                            <i class="fas fa-car fa-2x text-primary"></i>
                            <small class="d-block mt-1">سيارة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-truck">
                            <i class="fas fa-truck fa-2x text-primary"></i>
                            <small class="d-block mt-1">شاحنة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-motorcycle">
                            <i class="fas fa-motorcycle fa-2x text-primary"></i>
                            <small class="d-block mt-1">دراجة نارية</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-bus">
                            <i class="fas fa-bus fa-2x text-primary"></i>
                            <small class="d-block mt-1">حافلة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-taxi">
                            <i class="fas fa-taxi fa-2x text-primary"></i>
                            <small class="d-block mt-1">تاكسي</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-shuttle-van">
                            <i class="fas fa-shuttle-van fa-2x text-primary"></i>
                            <small class="d-block mt-1">فان</small>
                        </div>
                    </div>
                    <!-- أيقونات عامة -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-star">
                            <i class="fas fa-star fa-2x text-warning"></i>
                            <small class="d-block mt-1">نجمة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-crown">
                            <i class="fas fa-crown fa-2x text-warning"></i>
                            <small class="d-block mt-1">تاج</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-gem">
                            <i class="fas fa-gem fa-2x text-info"></i>
                            <small class="d-block mt-1">جوهرة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-fire">
                            <i class="fas fa-fire fa-2x text-danger"></i>
                            <small class="d-block mt-1">نار</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-bolt">
                            <i class="fas fa-bolt fa-2x text-warning"></i>
                            <small class="d-block mt-1">برق</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-tag">
                            <i class="fas fa-tag fa-2x text-secondary"></i>
                            <small class="d-block mt-1">علامة</small>
                        </div>
                    </div>

                    <!-- أيقونات الأعمال والتجارة -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-store">
                            <i class="fas fa-store fa-2x text-success"></i>
                            <small class="d-block mt-1">متجر</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-shopping-cart">
                            <i class="fas fa-shopping-cart fa-2x text-success"></i>
                            <small class="d-block mt-1">عربة تسوق</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-shopping-bag">
                            <i class="fas fa-shopping-bag fa-2x text-success"></i>
                            <small class="d-block mt-1">حقيبة تسوق</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-credit-card">
                            <i class="fas fa-credit-card fa-2x text-info"></i>
                            <small class="d-block mt-1">بطاقة ائتمان</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-dollar-sign">
                            <i class="fas fa-dollar-sign fa-2x text-success"></i>
                            <small class="d-block mt-1">دولار</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-chart-line">
                            <i class="fas fa-chart-line fa-2x text-primary"></i>
                            <small class="d-block mt-1">مخطط</small>
                        </div>
                    </div>

                    <!-- أيقونات التكنولوجيا -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-laptop">
                            <i class="fas fa-laptop fa-2x text-dark"></i>
                            <small class="d-block mt-1">لابتوب</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-mobile-alt">
                            <i class="fas fa-mobile-alt fa-2x text-dark"></i>
                            <small class="d-block mt-1">هاتف</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-tablet-alt">
                            <i class="fas fa-tablet-alt fa-2x text-dark"></i>
                            <small class="d-block mt-1">تابلت</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-desktop">
                            <i class="fas fa-desktop fa-2x text-dark"></i>
                            <small class="d-block mt-1">حاسوب</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-gamepad">
                            <i class="fas fa-gamepad fa-2x text-purple"></i>
                            <small class="d-block mt-1">ألعاب</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-camera">
                            <i class="fas fa-camera fa-2x text-dark"></i>
                            <small class="d-block mt-1">كاميرا</small>
                        </div>
                    </div>

                    <!-- أيقونات الرياضة والترفيه -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-futbol">
                            <i class="fas fa-futbol fa-2x text-success"></i>
                            <small class="d-block mt-1">كرة قدم</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-basketball-ball">
                            <i class="fas fa-basketball-ball fa-2x text-warning"></i>
                            <small class="d-block mt-1">كرة سلة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-dumbbell">
                            <i class="fas fa-dumbbell fa-2x text-danger"></i>
                            <small class="d-block mt-1">رياضة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-swimming-pool">
                            <i class="fas fa-swimming-pool fa-2x text-info"></i>
                            <small class="d-block mt-1">سباحة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-music">
                            <i class="fas fa-music fa-2x text-purple"></i>
                            <small class="d-block mt-1">موسيقى</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-film">
                            <i class="fas fa-film fa-2x text-dark"></i>
                            <small class="d-block mt-1">أفلام</small>
                        </div>
                    </div>

                    <!-- أيقونات الصحة والطب -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-heartbeat">
                            <i class="fas fa-heartbeat fa-2x text-danger"></i>
                            <small class="d-block mt-1">صحة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-stethoscope">
                            <i class="fas fa-stethoscope fa-2x text-info"></i>
                            <small class="d-block mt-1">طب</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-pills">
                            <i class="fas fa-pills fa-2x text-success"></i>
                            <small class="d-block mt-1">أدوية</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-hospital">
                            <i class="fas fa-hospital fa-2x text-primary"></i>
                            <small class="d-block mt-1">مستشفى</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-user-md">
                            <i class="fas fa-user-md fa-2x text-info"></i>
                            <small class="d-block mt-1">طبيب</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-first-aid">
                            <i class="fas fa-first-aid fa-2x text-danger"></i>
                            <small class="d-block mt-1">إسعافات</small>
                        </div>
                    </div>

                    <!-- أيقونات الطعام والشراب -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-utensils">
                            <i class="fas fa-utensils fa-2x text-warning"></i>
                            <small class="d-block mt-1">طعام</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-coffee">
                            <i class="fas fa-coffee fa-2x text-dark"></i>
                            <small class="d-block mt-1">قهوة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-pizza-slice">
                            <i class="fas fa-pizza-slice fa-2x text-warning"></i>
                            <small class="d-block mt-1">بيتزا</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-hamburger">
                            <i class="fas fa-hamburger fa-2x text-warning"></i>
                            <small class="d-block mt-1">برجر</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-ice-cream">
                            <i class="fas fa-ice-cream fa-2x text-info"></i>
                            <small class="d-block mt-1">آيس كريم</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-wine-glass">
                            <i class="fas fa-wine-glass fa-2x text-purple"></i>
                            <small class="d-block mt-1">مشروبات</small>
                        </div>
                    </div>

                    <!-- أيقونات التعليم والكتب -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-book">
                            <i class="fas fa-book fa-2x text-primary"></i>
                            <small class="d-block mt-1">كتاب</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-graduation-cap">
                            <i class="fas fa-graduation-cap fa-2x text-success"></i>
                            <small class="d-block mt-1">تعليم</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-school">
                            <i class="fas fa-school fa-2x text-info"></i>
                            <small class="d-block mt-1">مدرسة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-university">
                            <i class="fas fa-university fa-2x text-primary"></i>
                            <small class="d-block mt-1">جامعة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-pen">
                            <i class="fas fa-pen fa-2x text-dark"></i>
                            <small class="d-block mt-1">قلم</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-calculator">
                            <i class="fas fa-calculator fa-2x text-secondary"></i>
                            <small class="d-block mt-1">حاسبة</small>
                        </div>
                    </div>

                    <!-- أيقونات السفر والسياحة -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-plane">
                            <i class="fas fa-plane fa-2x text-info"></i>
                            <small class="d-block mt-1">طائرة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-ship">
                            <i class="fas fa-ship fa-2x text-primary"></i>
                            <small class="d-block mt-1">سفينة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-train">
                            <i class="fas fa-train fa-2x text-success"></i>
                            <small class="d-block mt-1">قطار</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-suitcase">
                            <i class="fas fa-suitcase fa-2x text-warning"></i>
                            <small class="d-block mt-1">حقيبة سفر</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-map">
                            <i class="fas fa-map fa-2x text-info"></i>
                            <small class="d-block mt-1">خريطة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-compass">
                            <i class="fas fa-compass fa-2x text-secondary"></i>
                            <small class="d-block mt-1">بوصلة</small>
                        </div>
                    </div>

                    <!-- أيقونات الطبيعة والطقس -->
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-sun">
                            <i class="fas fa-sun fa-2x text-warning"></i>
                            <small class="d-block mt-1">شمس</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-moon">
                            <i class="fas fa-moon fa-2x text-secondary"></i>
                            <small class="d-block mt-1">قمر</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-cloud">
                            <i class="fas fa-cloud fa-2x text-info"></i>
                            <small class="d-block mt-1">غيمة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-tree">
                            <i class="fas fa-tree fa-2x text-success"></i>
                            <small class="d-block mt-1">شجرة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-leaf">
                            <i class="fas fa-leaf fa-2x text-success"></i>
                            <small class="d-block mt-1">ورقة</small>
                        </div>
                    </div>
                    <div class="col-2 mb-3">
                        <div class="text-center p-2 border rounded icon-item" data-icon="fas fa-mountain">
                            <i class="fas fa-mountain fa-2x text-secondary"></i>
                            <small class="d-block mt-1">جبل</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>



<?php
// إضافة JavaScript مخصص
$customScript = <<<'EOD'
    // تحديث معاينة الأيقونة في نموذج الإضافة
    $("#iconInput").on("input", function() {
        var iconClass = $(this).val() || "fas fa-tag";
        $("#iconPreview").attr("class", iconClass + " text-primary");
    });

    // تحديث معاينة الأيقونة في نموذج التعديل
    $("#editIconInput").on("input", function() {
        var iconClass = $(this).val() || "fas fa-tag";
        $("#editIconPreview").attr("class", iconClass + " text-primary");
    });

    // تعديل الفئة
    $(".edit-category").on("click", function() {
        var id = $(this).data("id");
        var nameAr = $(this).data("name-ar");
        var nameEn = $(this).data("name-en");
        var icon = $(this).data("icon");

        $("#editCategoryId").val(id);
        $("#editCategoryAr").val(nameAr);
        $("#editCategoryEn").val(nameEn);
        $("#editIconInput").val(icon);
        $("#editIconPreview").attr("class", (icon || "fas fa-tag") + " text-primary");

        $("#editCategoryModal").modal("show");
    });

    // إرسال نموذج التعديل
    $("#editCategoryForm").on("submit", function(e) {
        e.preventDefault();

        var formData = $(this).serialize();

        sendAjaxRequest("index.php", formData, function(response) {
            $("#editCategoryModal").modal("hide");
            location.reload();
        });
    });

    // تفعيل DataTable
    if ($("#categoriesTable").length) {
        $("#categoriesTable").DataTable({
            language: {
                url: "https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json"
            },
            responsive: true,
            pageLength: 10,
            order: [[1, "asc"]],
            dom: 'lrtip'  // إخفاء حقل البحث الافتراضي مع الحفاظ على وظيفة البحث
        });
    }

    // وظيفة البحث في الجدول
    $('#searchInput').on('keyup', function() {
        if ($.fn.DataTable.isDataTable('#categoriesTable')) {
            $('#categoriesTable').DataTable().search(this.value).draw();
        }
    });

    // مسح البحث
    $('#clearSearch').on('click', function() {
        $('#searchInput').val('');
        if ($.fn.DataTable.isDataTable('#categoriesTable')) {
            $('#categoriesTable').DataTable().search('').draw();
        }
    });

    // تفعيل tooltips Bootstrap 5
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // معالج اختيار الأيقونة - مع تأخير للتأكد من تحميل العناصر
    $(document).on('click', '.icon-item', function() {
        var iconClass = $(this).data('icon');
        $('#iconInput').val(iconClass);
        $('#iconPreview').removeClass().addClass(iconClass + ' text-primary');
        $('#iconPickerModal').modal('hide');

        // إضافة تأكيد بصري
        $(this).addClass('border-success bg-success bg-opacity-25');
        setTimeout(() => {
            $(this).removeClass('border-success bg-success bg-opacity-25');
        }, 300);
    });

    // البحث في الأيقونات - مع event delegation
    $(document).on('keyup', '#iconSearch', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('.icon-item').each(function() {
            var iconName = $(this).find('small').text().toLowerCase();
            var iconClass = $(this).data('icon').toLowerCase();
            var parentCol = $(this).closest('.col-2');

            if (iconName.includes(searchTerm) || iconClass.includes(searchTerm)) {
                parentCol.show();
            } else {
                parentCol.hide();
            }
        });
    });

    // فتح نموذج اختيار الأيقونة
    window.openIconPicker = function() {
        $('#addCategoryModal').modal('hide');
        setTimeout(function() {
            $('#iconPickerModal').modal('show');
        }, 500);
    };

    // معالج اختيار الأيقونة
    $(document).on('click', '.icon-item', function() {
        var iconClass = $(this).data('icon');
        var iconHtml = '<i class="' + iconClass + '"></i>';

        // تحديث الحقل بـ HTML الكامل
        $('#iconInput').val(iconHtml);

        // تحديث المعاينة
        $('#iconPreview').removeClass().addClass(iconClass + ' text-primary');
        $('#iconPickerModal').modal('hide');

        // العودة لنموذج إضافة الفئة
        setTimeout(function() {
            $('#addCategoryModal').modal('show');
        }, 500);

        // تأكيد بصري
        $(this).addClass('border-success bg-success bg-opacity-25');
        setTimeout(function() {
            $('.icon-item').removeClass('border-success bg-success bg-opacity-25');
        }, 300);
    });

    // البحث في الأيقونات
    $(document).on('keyup', '#iconSearch', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('.icon-item').each(function() {
            var iconName = $(this).find('small').text().toLowerCase();
            var iconClass = $(this).data('icon').toLowerCase();
            var parentCol = $(this).closest('.col-2');

            if (searchTerm === '' || iconName.includes(searchTerm) || iconClass.includes(searchTerm)) {
                parentCol.show();
            } else {
                parentCol.hide();
            }
        });
    });

    // تفعيل hover effects
    $(document).on('mouseenter', '.icon-item', function() {
        $(this).addClass('bg-light border-primary');
    });

    $(document).on('mouseleave', '.icon-item', function() {
        $(this).removeClass('bg-light border-primary');
    });
EOD;

include '../../includes/footer.php';
?>