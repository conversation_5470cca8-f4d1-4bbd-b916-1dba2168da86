<?php

/**
 * صفحة عرض تفاصيل السيارة
 * Car Details View Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// التحقق من معرف السيارة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف السيارة غير صحيح';
    header('Location: index.php');
    exit();
}

$carId = intval($_GET['id']);

try {
    // استرجاع بيانات السيارة
    $carQuery = "
        SELECT c.*, cat.category_ar, cat.category_en, cat.icon
        FROM cars c 
        LEFT JOIN car_categories cat ON c.category_id = cat.category_id 
        WHERE c.car_id = :car_id
    ";
    $car = $db->Fetch($carQuery, ['car_id' => $carId]);

    if (!$car) {
        $_SESSION['error_message'] = 'السيارة غير موجودة';
        header('Location: index.php');
        exit();
    }

    // استرجاع صور السيارة
    $carImages = $db->FetchAll("
        SELECT * FROM car_images 
        WHERE car_id = :car_id 
        ORDER BY is_main DESC, image_id ASC
    ", ['car_id' => $carId]);

    // استرجاع مميزات السيارة
    $carFeatures = $db->FetchAll("
        SELECT * FROM car_features 
        WHERE car_id = :car_id 
        ORDER BY feature_type_ar, feature_name_ar
    ", ['car_id' => $carId]);
} catch (Exception $e) {
    $_SESSION['error_message'] = 'حدث خطأ أثناء استرجاع بيانات السيارة: ' . $e->getMessage();
    header('Location: index.php');
    exit();
}

// عنوان الصفحة
$pageTitle = 'تفاصيل السيارة - ' . $car['brand_ar'] . ' ' . $car['model_ar'];

// تضمين ملف الرأس
include '../../includes/header.php';
?>

<!-- محتوى صفحة تفاصيل السيارة -->
<div class="container-fluid p-4">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar']); ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../../dashboard.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">إدارة السيارات</a></li>
                    <li class="breadcrumb-item active">تفاصيل السيارة</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="edit.php?id=<?php echo $car['car_id']; ?>" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            <a href="manage_images.php?id=<?php echo $car['car_id']; ?>" class="btn btn-info">
                <i class="fas fa-images me-2"></i>
                إدارة الصور
            </a>
            <a href="manage_features.php?id=<?php echo $car['car_id']; ?>" class="btn btn-primary">
                <i class="fas fa-star me-2"></i>
                إدارة الميزات
            </a>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- القسم الأيسر - الصور -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>
                        صور السيارة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($carImages)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-camera text-muted" style="font-size: 4rem;"></i>
                            <p class="text-muted mt-3">لا توجد صور للسيارة</p>
                        </div>
                    <?php else: ?>
                        <!-- الصورة الرئيسية -->
                        <div class="main-image-container mb-3">
                            <img src="<?php echo UPLOAD_PATH . $carImages[0]['image_url']; ?>"
                                alt="<?php echo htmlspecialchars($car['brand_ar'] . ' ' . $car['model_ar']); ?>"
                                class="img-fluid rounded main-car-image"
                                id="mainImage"
                                style="width: 100%; height: 300px; object-fit: cover;">
                        </div>

                        <!-- الصور المصغرة -->
                        <?php if (count($carImages) > 1): ?>
                            <div class="thumbnails-container">
                                <div class="row g-2">
                                    <?php foreach ($carImages as $index => $image):
                                        if ($image['is_main'] == 0): ?>
                                            <div class="col-3">
                                                <img src="<?php echo UPLOAD_PATH . $image['image_url']; ?>"
                                                    alt="صورة <?php echo $index + 1; ?>"
                                                    class="img-thumbnail car-thumbnail <?php echo $index === 0 ? 'active' : ''; ?>"
                                                    style="width: 100%; height: 80px; object-fit: cover; cursor: pointer;"
                                                    onclick="changeMainImage('<?php echo UPLOAD_PATH . $image['image_url']; ?>', this)">
                                            </div>
                                    <?php
                                        endif;
                                    endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- القسم الأيمن - البيانات الأساسية -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        البيانات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- الحالة -->
                        <div class="col-12 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">حالة السيارة:</span>
                                <?php if ($car['is_sold']): ?>
                                    <span class="badge bg-warning fs-6">
                                        <i class="fas fa-handshake me-1"></i>مباع
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check-circle me-1"></i>متاح للبيع
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- السعر -->
                        <div class="col-12 mb-3">
                            <div class="alert alert-info">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-bold">السعر:</span>
                                    <span class="h4 mb-0 text-primary">
                                        <?php echo number_format($car['price']); ?> <?php echo $car['currency']; ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- الماركة والموديل -->
                        <div class="col-md-6 mb-3">
                            <strong>الماركة:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($car['brand_ar']); ?></span>
                            <?php if ($car['brand_en']): ?>
                                <br><small class="text-muted"><?php echo htmlspecialchars($car['brand_en']); ?></small>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong>الموديل:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($car['model_ar']); ?></span>
                            <?php if ($car['model_en']): ?>
                                <br><small class="text-muted"><?php echo htmlspecialchars($car['model_en']); ?></small>
                            <?php endif; ?>
                        </div>

                        <!-- السنة والكيلومترات -->
                        <div class="col-md-6 mb-3">
                            <strong>سنة الصنع:</strong><br>
                            <span class="badge bg-secondary"><?php echo $car['year']; ?></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong>الكيلومترات:</strong><br>
                            <span class="text-muted"><?php echo number_format($car['mileage']); ?> كم</span>
                        </div>

                        <!-- ناقل الحركة ونوع الوقود -->
                        <div class="col-md-6 mb-3">
                            <strong>ناقل الحركة:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($car['transmission_ar']); ?></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong>نوع الوقود:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($car['fuel_type_ar']); ?></span>
                        </div>

                        <!-- حجم المحرك واللون -->
                        <div class="col-md-6 mb-3">
                            <strong>حجم المحرك:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($car['engine_size']); ?></span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong>اللون:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($car['color_ar']); ?></span>
                        </div>

                        <!-- عدد الأبواب والمقاعد -->
                        <div class="col-md-6 mb-3">
                            <strong>عدد الأبواب:</strong><br>
                            <span class="badge bg-info"><?php echo $car['doors']; ?> أبواب</span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong>عدد المقاعد:</strong><br>
                            <span class="badge bg-info"><?php echo $car['seats']; ?> مقاعد</span>
                        </div>

                        <!-- الفئة والموقع -->
                        <div class="col-md-6 mb-3">
                            <strong>الفئة:</strong><br>
                            <?php if ($car['category_ar']): ?>
                                <span class="badge bg-primary">
                                    <?php if ($car['icon']): ?>
                                        <?php echo $car['icon']; ?>
                                    <?php endif; ?>
                                    <?php echo htmlspecialchars($car['category_ar']); ?>
                                </span>
                            <?php else: ?>
                                <span class="text-muted">غير محدد</span>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <strong>الموقع:</strong><br>
                            <span class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($car['location_ar']); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الوصف -->
        <?php if ($car['description_ar'] || $car['description_en']): ?>
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-align-right me-2"></i>
                            الوصف
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($car['description_ar']): ?>
                            <div class="mb-3">
                                <strong>الوصف (العربية):</strong>
                                <p class="mt-2"><?php echo nl2br(htmlspecialchars($car['description_ar'])); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if ($car['description_en']): ?>
                            <div>
                                <strong>الوصف (الإنجليزية):</strong>
                                <p class="mt-2"><?php echo nl2br(htmlspecialchars($car['description_en'])); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- المميزات -->
        <?php if (!empty($carFeatures)): ?>
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star me-2"></i>
                            المميزات والخصائص
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // تجميع المميزات حسب النوع
                        $featuresGrouped = [];
                        foreach ($carFeatures as $feature) {
                            $featuresGrouped[$feature['feature_type_ar']][] = $feature;
                        }
                        ?>

                        <?php foreach ($featuresGrouped as $featureType => $features): ?>
                            <div class="mb-3">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-cog me-1"></i>
                                    <?php echo htmlspecialchars($featureType); ?>
                                </h6>
                                <div class="row">
                                    <?php foreach ($features as $feature): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span><?php echo htmlspecialchars($feature['feature_name_ar']); ?>:</span>
                                                <span class="text-muted">
                                                    <?php echo htmlspecialchars($feature['feature_value_ar'] ?: 'متوفر'); ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- معلومات إضافية -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>تاريخ الإضافة:</strong><br>
                            <span class="text-muted">
                                <i class="fas fa-calendar-plus me-1"></i>
                                <?php echo formatDateArabic($car['created_at']); ?>
                            </span>
                        </div>

                        <div class="col-md-4">
                            <strong>آخر تحديث:</strong><br>
                            <span class="text-muted">
                                <i class="fas fa-calendar-check me-1"></i>
                                <?php echo formatDateArabic($car['updated_at']); ?>
                            </span>
                        </div>

                        <div class="col-md-4">
                            <strong>معرف السيارة:</strong><br>
                            <span class="badge bg-secondary">#<?php echo $car['car_id']; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض الصورة بحجم كامل -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">عرض الصورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
    // تغيير الصورة الرئيسية
    function changeMainImage(imageSrc, thumbnailElement) {
        document.getElementById('mainImage').src = imageSrc;

        // إزالة الفئة النشطة من جميع الصور المصغرة
        document.querySelectorAll('.car-thumbnail').forEach(thumb => {
            thumb.classList.remove('active');
        });

        // إضافة الفئة النشطة للصورة المحددة
        thumbnailElement.classList.add('active');
    }

    // عرض الصورة في نافذة منبثقة
    document.getElementById('mainImage').addEventListener('click', function() {
        document.getElementById('modalImage').src = this.src;
        new bootstrap.Modal(document.getElementById('imageModal')).show();
    });

    // إضافة تأثيرات CSS للصور المصغرة
    document.addEventListener('DOMContentLoaded', function() {
        const style = document.createElement('style');
        style.textContent = `
        .car-thumbnail {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .car-thumbnail:hover {
            transform: scale(1.05);
            border-color: #007bff;
        }

        .car-thumbnail.active {
            border-color: #007bff;
            box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
        }

        .main-car-image {
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .main-car-image:hover {
            transform: scale(1.02);
        }
    `;
        document.head.appendChild(style);
    });
</script>

<?php include '../../includes/footer.php'; ?>