/**
 * ملف الأنماط المخصصة للوحة التحكم
 * Custom CSS for Admin Dashboard
 */

/* متغيرات الألوان */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --sidebar-width: 280px;
    --transition: all 0.3s ease;
}

/* الخط الأساسي */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f4f6f9;
    font-size: 14px;
    line-height: 1.6;
}

/* تحسينات الجداول */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
    text-align: center; /* توسيط جميع عناوين الجداول */
}

/* توسيط عناوين الجداول الداكنة أيضاً */
.table-dark th,
.table thead th {
    text-align: center !important;
}

/* توسيط قوي لجميع عناوين الجداول */
table th,
.table th,
.table-dark th,
.table thead th,
thead th,
th {
    text-align: center !important;
}

.table td {
    border: none;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #f1f3f4;
    border-radius: 10px 10px 0 0 !important;
    padding: 1.25rem 1.5rem;
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
}

/* تحسينات الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, var(--secondary-color) 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* تحسينات النماذج */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* تحسينات التنبيهات */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

/* بطاقات الإحصائيات */
.stat-card {
    transition: var(--transition);
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.stat-label {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

/* تحسينات الصور */
.img-thumbnail {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: var(--transition);
}

.img-thumbnail:hover {
    border-color: var(--secondary-color);
    transform: scale(1.05);
}

.car-thumbnail {
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
}

.car-thumbnail:hover,
.car-thumbnail.active {
    opacity: 1;
    border-color: var(--secondary-color);
}

/* تحسينات الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 6px;
}

/* تحسينات التنقل */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 0.5rem;
}

.breadcrumb-item a {
    color: var(--secondary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* تحسينات DataTables */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin: 1rem 0;
}

.dataTables_wrapper .dataTables_filter input {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: none;
    color: var(--secondary-color);
}

.page-link:hover {
    background-color: var(--secondary-color);
    color: white;
}

.page-item.active .page-link {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* تحسينات النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #f1f3f4;
    border-radius: 10px 10px 0 0;
}

.modal-footer {
    border-top: 1px solid #f1f3f4;
    border-radius: 0 0 10px 10px;
}

/* تحسينات خاصة بالسيارات */
.car-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.car-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.car-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.car-image {
    height: 200px;
    object-fit: cover;
    border-radius: 15px 15px 0 0;
}

.car-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--success-color);
}

/* تحسينات أيقونات المستخدمين */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

/* تحسينات الخريطة */
.map-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* تحسينات رفع الملفات */
.file-upload-area {
    border: 2px dashed #e9ecef;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

/* تحسينات الحقول الديناميكية */
.dynamic-field {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.dynamic-field:hover {
    border-color: var(--secondary-color);
}

.remove-field {
    float: left;
    margin-top: 0.5rem;
}

/* تحسينات الاستجابة للجوال */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem !important;
    }

    .card-body {
        padding: 1rem;
    }

    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}

@media (max-width: 576px) {
    .btn-group .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .card-header {
        padding: 1rem;
    }

    .breadcrumb {
        font-size: 0.8rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .topbar,
    .btn,
    .pagination,
    .no-print {
        display: none !important;
    }

    .main-content {
        margin-right: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .table {
        font-size: 0.8rem;
    }
}

/* تحسينات إضافية */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* تحسينات إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* تحسينات التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}