<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

// بدء الجلسة
session_start();

// تضمين الملفات المطلوبة
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/session.php';

// إذا كان المستخدم مسجل دخول بالفعل، توجيهه للوحة التحكم
if (isset($_SESSION['user_id'])) {
    header('Location: ../dashboard.php');
    exit();
}

$error = '';
$success = '';

// معالجة طلب تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    // التحقق من البيانات المدخلة
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            // البحث عن المستخدم في قاعدة البيانات
            $stmt = "SELECT id, fullname, username, password, is_admin FROM users WHERE username = :username";
            $user = $db->Fetch($stmt, ['username' => $username]);

            if ($user && verifyPassword($password, $user['password'])) {
                // تسجيل الدخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['fullname'] = $user['fullname'];
                $_SESSION['is_admin'] = $user['is_admin'];
                $_SESSION['last_activity'] = time();

                // تسجيل محاولة الدخول الناجحة
                logLoginAttempt($username, true);

                // توجيه المستخدم للوحة التحكم
                header('Location: ../dashboard.php');
                exit();
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                // تسجيل محاولة الدخول الفاشلة
                logLoginAttempt($username, false);
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>

    <!-- Bootstrap 5.3 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-header i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }

        .login-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1.5rem;
        }

        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }

        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }

        .input-group:focus-within .input-group-text {
            border-color: #667eea;
        }

        .form-check {
            margin: 1.5rem 0;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
                border-radius: 15px;
            }

            .login-header {
                padding: 1.5rem;
            }

            .login-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- رأس صفحة تسجيل الدخول -->
        <div class="login-header">
            <i class="fas fa-user-shield"></i>
            <h2>لوحة التحكم</h2>
            <p class="mb-0">يرجى تسجيل الدخول للمتابعة</p>
        </div>

        <!-- محتوى صفحة تسجيل الدخول -->
        <div class="login-body">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <!-- حقل اسم المستخدم -->
                <div class="input-group mb-3">
                    <input type="text" 
                           class="form-control with-icon" 
                           name="username" 
                           placeholder="اسم المستخدم"
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           required>
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                </div>

                <!-- حقل كلمة المرور -->
                <div class="input-group mb-3">
                    <input type="password" 
                           class="form-control with-icon" 
                           name="password" 
                           placeholder="كلمة المرور"
                           required>
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- تذكرني -->
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="remember" id="remember">
                    <label class="form-check-label" for="remember">
                        تذكرني
                    </label>
                </div>

                <!-- زر تسجيل الدخول -->
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

    <script>
        // تركيز على حقل اسم المستخدم عند تحميل الصفحة
        $(document).ready(function() {
            $('input[name="username"]').focus();

            // إضافة تأثيرات بصرية للحقول
            $('.form-control').on('focus', function() {
                $(this).parent().addClass('focused');
            });

            $('.form-control').on('blur', function() {
                $(this).parent().removeClass('focused');
            });
        });

        // منع إرسال النموذج إذا كانت الحقول فارغة
        $('form').on('submit', function(e) {
            var username = $('input[name="username"]').val().trim();
            var password = $('input[name="password"]').val().trim();

            if (!username || !password) {
                e.preventDefault();
                alert('يرجى إدخال اسم المستخدم وكلمة المرور');
                return false;
            }
        });
    </script>
</body>
</html>