<?php
session_start();

include_once '../includes/config.php';
include_once '../includes/functions.php';

$lang = getCurrentLanguage();
include_once 'lang/' . $lang . '.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $car_id = isset($_POST['car_id']) && is_numeric($_POST['car_id']) ? intval($_POST['car_id']) : 0;
    $reviewer_name = trim($_POST['reviewer_name'] ?? '');
    $reviewer_email = trim($_POST['reviewer_email'] ?? '');
    $rating = isset($_POST['rating']) && is_numeric($_POST['rating']) ? intval($_POST['rating']) : 0;
    $review_title = trim($_POST['review_title'] ?? '');
    $review_content = trim($_POST['review_content'] ?? '');

    // Validation
    $errors = [];

    if (empty($reviewer_name)) {
        $errors[] = ($lang == 'ar') ? 'الاسم مطلوب' : 'Name is required';
    }

    if (empty($reviewer_email) || !filter_var($reviewer_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = ($lang == 'ar') ? 'البريد الإلكتروني غير صحيح' : 'Invalid email address';
    }

    if ($rating < 1 || $rating > 5) {
        $errors[] = ($lang == 'ar') ? 'التقييم يجب أن يكون بين 1 و 5' : 'Rating must be between 1 and 5';
    }

    if (empty($review_content)) {
        $errors[] = ($lang == 'ar') ? 'محتوى التقييم مطلوب' : 'Review content is required';
    }

    if ($car_id <= 0) {
        $errors[] = ($lang == 'ar') ? 'معرف السيارة غير صحيح' : 'Invalid car ID';
    }

    if (empty($errors)) {
        try {
            $reviewData = [
                'car_id' => $car_id,
                'reviewer_name' => $reviewer_name,
                'reviewer_email' => $reviewer_email,
                'rating' => $rating,
                'review_title' => $review_title,
                'review_content' => $review_content
            ];

            $review_id = addCarReview($reviewData);

            if ($review_id) {
                $_SESSION['review_success'] = ($lang == 'ar') ?
                    'تم إرسال تقييمك بنجاح! سيتم مراجعته قبل النشر.' :
                    'Your review has been submitted successfully! It will be reviewed before publishing.';
            } else {
                $_SESSION['review_error'] = ($lang == 'ar') ?
                    'حدث خطأ أثناء إرسال التقييم. يرجى المحاولة مرة اخرى.' :
                    'An error occurred while submitting your review. Please try again.';
            }
        } catch (Exception $e) {
            $_SESSION['review_error'] = ($lang == 'ar') ?
                'حدث خطأ أثناء إرسال التقييم. يرجى المحاولة مرة أخرى.' :
                'An error occurred while submitting your review. Please try again.';
        }
    } else {
        $_SESSION['review_error'] = implode('<br>', $errors);
    }

    // Redirect back to car details page
    header('Location: ' . createLink('car_details.php', true, $car_id));
    exit();
} else {
    // If not POST request, redirect to cars page
    header('Location: ' . createLink('cars.php'));
    exit();
}
