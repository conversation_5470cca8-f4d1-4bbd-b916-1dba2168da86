<?php
$currentLang = getCurrentLanguage();

?>

<!-- About Us Hero Section -->
<section class="hero-section position-relative text-center" style="padding-top: 100px;">

    <div class="animated-bg" data-url="<?php echo ASSETS_PATH; ?>" style=" background-image: url('<?php echo ASSETS_PATH; ?>images/bumpy.webp');"></div>
    <div class="container" style="z-index: 2;">
        <div class="row min-vh-80 align-items-center">
            <div class="text-white flex flex-col gap-5 pb-5 w-full position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <h1 class="display-4 fw-bold mb-3 text-white animate__animated animate__fadeInDown" style="text-transform: uppercase; letter-spacing: 1.5px;"><?php echo $lang['about_title']; ?></h1>
                <div class="divider mx-auto mb-4" style="height: 4px; width: 70px; background-color: #05141f;"></div>
                <div class="text-rotator">
                    <p class="lead mb-4 text-white rotating-text active"><?php echo $lang['hero_subtitle1']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle2']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle3']; ?></p>
                    <p class="lead mb-4 text-white rotating-text"><?php echo $lang['hero_subtitle4']; ?></p>
                </div>
            </div>
        </div>
    </div>
    <svg data-name="VIDEO GRADIENT" xmlns="http://www.w3.org/2000/svg" class="home-hero__gradient" role="presentation">
        <defs data-v-1bfe21c6="">
            <linearGradient id="DesktopGradient_svg__a" x1=".5" y1=".129" x2=".5" y2=".708" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-opacity="0"></stop>
                <stop offset=".364" stop-opacity=".424"></stop>
                <stop offset=".64" stop-color="#030b11" stop-opacity=".733"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
            <linearGradient id="DesktopGradient_svg__b" x1=".5" y1=".129" x2=".5" y2=".5" gradientUnits="objectBoundingBox">
                <stop offset="0" stop-color="#05141f" stop-opacity="0"></stop>
                <stop offset=".226" stop-color="#05141f" stop-opacity=".082"></stop>
                <stop offset=".512" stop-color="#05141f" stop-opacity=".271"></stop>
                <stop offset="1" stop-color="#05141f"></stop>
            </linearGradient>
        </defs>
        <rect fill="url(#DesktopGradient_svg__a)" width="100%" height="204"></rect>
        <rect fill="url(#DesktopGradient_svg__b)" y="72" width="100%" height="132"></rect>
    </svg>
</section>

<!-- About Us Section -->
<section class="about-us-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <div class="position-sticky" style="top: 2rem;">
                    <img src="<?php echo ASSETS_PATH ?>images/about-us.png" alt="<?php echo $lang['hero_title']; ?>" class="img-fluid rounded shadow">
                </div>
            </div>
            <div class="col-lg-6">
                <h2 class="display-5 fw-bold text-primary mb-4"><?php echo $lang['about_title']; ?></h2>
                <p class="lead text-muted" style="text-align: justify;">
                    <?php echo $lang['about_content']; ?>
                </p>
                <div class="mt-4">
                    <h5 class="fw-bold text-dark"><?php echo $lang['about_mission']; ?></h5>

                    <p class="text-muted" style="text-align: justify;">
                        <?php echo $lang['about_mission_content']; ?>
                    </p>
                </div>
                <div class="mt-4">
                    <h5 class="fw-bold text-dark"><?php echo $lang['about_vision']; ?></h5>
                    <p class="text-muted" style="text-align: justify;">
                        <?php echo $lang['about_vision_content']; ?>
                    </p>
                </div>
                <div class="mt-4">
                    <h5 class="fw-bold text-dark"><?php echo $lang['about_objectives']; ?></h5>
                    <p class="text-muted" style="text-align: justify;">
                        <?php echo $lang['about_objectives_content']; ?>
                    </p>
                </div>
                <div class="mt-4">
                    <h5 class="fw-bold text-dark"><?php echo $lang['about_values']; ?></h5>
                    <ul class="list-unstyled text-muted">
                        <li class="mb-2"><i class="fas fa-check-circle text-primary me-2"></i> <?php echo $lang['about_values1']; ?></li>
                        <li class="mb-2"><i class="fas fa-check-circle text-primary me-2"></i> <?php echo $lang['about_values2']; ?></li>
                        <li class="mb-2"><i class="fas fa-check-circle text-primary me-2"></i> <?php echo $lang['about_values3']; ?></li>
                        <li class="mb-2"><i class="fas fa-check-circle text-primary me-2"></i> <?php echo $lang['about_values4']; ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <div class="bg-white p-4 rounded shadow-sm">
                    <h3 class="text-center text-primary mb-4"><?php echo $lang['about_why_choose_us']; ?></h3>

                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <div class="icon-lg bg-primary-light text-primary rounded-circle mb-3 mx-auto">
                                    <i class="fas fa-shield-alt fa-2x"></i>
                                </div>
                                <h5 class="fw-bold"><?php echo $lang['about_safety']; ?></h5>
                                <p class="text-muted mb-0">
                                    <?php echo $lang['about_safety_content']; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <div class="icon-lg bg-primary-light text-primary rounded-circle mb-3 mx-auto">
                                    <i class="fas fa-car fa-2x"></i>
                                </div>
                                <h5 class="fw-bold"><?php echo $lang['about_best_offers']; ?></h5>
                                <p class="text-muted mb-0">
                                    <?php echo $lang['about_best_offers_content']; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3">
                                <div class="icon-lg bg-primary-light text-primary rounded-circle mb-3 mx-auto">
                                    <i class="fas fa-headset fa-2x"></i>
                                </div>
                                <h5 class="fw-bold"><?php echo $lang['about_professional_support']; ?></h5>
                                <p class="text-muted mb-0">
                                    <?php echo $lang['about_professional_support_content']; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const texts = document.querySelectorAll('.rotating-text');
        let currentIndex = 0;
        const intervalTime = 10000;

        function rotateText() {
            const currentActive = document.querySelector('.rotating-text.active');
            if (currentActive) {
                currentActive.classList.add('fade-out');
                currentActive.classList.remove('active');
            }

            currentIndex = (currentIndex + 1) % texts.length;

            setTimeout(() => {
                texts[currentIndex].classList.remove('fade-out');
                texts[currentIndex].classList.add('active');
            }, 300);
        }

        let rotationInterval = setInterval(rotateText, intervalTime);

    });
</script>