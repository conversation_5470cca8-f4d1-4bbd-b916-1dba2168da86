<?php
/**
 * حذف صورة السيارة
 * Delete Car Image
 */

require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
checkLogin();

// إعداد استجابة JSON
header('Content-Type: application/json');

$response = ['success' => false, 'message' => ''];

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة طلب غير صحيحة');
    }

    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        throw new Exception('رمز الأمان غير صحيح');
    }

    // التحقق من معرف الصورة
    if (!isset($_POST['image_id']) || !is_numeric($_POST['image_id'])) {
        throw new Exception('معرف الصورة غير صحيح');
    }

    $imageId = (int)$_POST['image_id'];

    // الحصول على بيانات الصورة
    $image = $db->Fetch("SELECT * FROM car_images WHERE image_id = :image_id", ['image_id' => $imageId]);

    if (!$image) {
        throw new Exception('الصورة غير موجودة');
    }

    // التحقق من صلاحية المستخدم (اختياري - يمكن إضافة فحص إضافي هنا)

    // حذف الملف من الخادم
    $imagePath = '../../uploads/' . $image['image_url'];
    if (file_exists($imagePath)) {
        if (!unlink($imagePath)) {
            // لا نتوقف عند فشل حذف الملف، نتابع لحذف السجل من قاعدة البيانات
            error_log("فشل في حذف ملف الصورة: " . $imagePath);
        }
    }

    // حذف السجل من قاعدة البيانات
    $deleted = $db->Remove("DELETE FROM car_images WHERE image_id = :image_id", ['image_id' => $imageId]);

    // التحقق من وجود الصورة بعد الحذف للتأكد من نجاح العملية
    $imageExists = $db->Fetch("SELECT image_id FROM car_images WHERE image_id = :image_id", ['image_id' => $imageId]);

    if (!$imageExists) {
        // إذا كانت الصورة الرئيسية، نحتاج لتعيين صورة أخرى كرئيسية
        if ($image['is_main'] == 1) {
            // البحث عن أول صورة أخرى لنفس السيارة وجعلها رئيسية
            $firstImage = $db->Fetch("SELECT image_id FROM car_images WHERE car_id = :car_id ORDER BY image_id ASC LIMIT 1", 
                ['car_id' => $image['car_id']]);

            if ($firstImage) {
                $db->Update("UPDATE car_images SET is_main = 1 WHERE image_id = :image_id", 
                    ['image_id' => $firstImage['image_id']]);
            }
        }

        $response['success'] = true;
        $response['message'] = 'تم حذف الصورة بنجاح';
    } else {
        throw new Exception('فشل في حذف الصورة من قاعدة البيانات');
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log("خطأ في حذف الصورة: " . $e->getMessage());
}

// إرسال الاستجابة
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
