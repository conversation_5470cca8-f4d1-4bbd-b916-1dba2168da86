$(document).ready(function () {
  'use strict';

  // Custom arrow functionality
  $('#prevArrow').click(function () {
    $('.kia-carousel').slick('slickPrev');
  });

  $('#nextArrow').click(function () {
    $('.kia-carousel').slick('slickNext');
  });

  // Add smooth hover effects for buttons
  $('.btn').hover(
    function () {
      $(this).addClass('hover-effect');
    },
    function () {
      $(this).removeClass('hover-effect');
    }
  );

  // Keyboard navigation
  $(document).keydown(function (e) {
    if (e.keyCode === 37) {
      // Left arrow key
      $('.kia-carousel').slick('slickPrev');
    } else if (e.keyCode === 39) {
      // Right arrow key
      $('.kia-carousel').slick('slickNext');
    }
  });

  // Touch/swipe support is already built into Slick

  // Add loading animation
  $('.car-image img').on('load', function () {
    $(this).addClass('loaded');
  });

  // Smooth scroll for any anchor links
  $('a[href^="#"]').click(function (e) {
    e.preventDefault();
    var target = $($(this).attr('href'));
    if (target.length) {
      $('html, body').animate(
        {
          scrollTop: target.offset().top,
        },
        600
      );
    }
  });

  // Add parallax effect to background (optional)
  $(window).scroll(function () {
    var scrolled = $(window).scrollTop();
    var parallax = $('.carousel-section');
    var speed = scrolled * 0.5;
    parallax.css('background-position', 'center ' + speed + 'px');
  });

  // Preload images for better performance
  function preloadImages() {
    var images = [
      'https://www.kia.com/content/dam/kia/us/en/home2-0/mtf-carousel/mpv/telluride/kia_telluride_2025_large-middle.png',
      'https://www.kia.com/content/dam/kia/us/en/home2-0/mtf-carousel/mpv/soul/kia_soul_2025_large-middle.png',
      'https://www.kia.com/content/dam/kia/us/en/home2-0/mtf-carousel/mpv/carnival/2025/kia_carnival-ice_2025_large-middle.png',
      'https://diyar-alkaram.com.iq/assets/images/kia_homepage_meet-the-family_background_suv-cuv-mpv-v8_XL.jpeg',
    ];

    images.forEach(function (src) {
      var img = new Image();
      img.src = src;
    });
  }

  preloadImages();

  // Add resize handler for responsive adjustments
  $(window).resize(function () {
    $('.kia-carousel').slick('setPosition');
  });

  // Add custom animation classes
  $('.kia-carousel').on(
    'beforeChange',
    function (event, slick, currentSlide, nextSlide) {
      $('.slide').removeClass('slide-active');
    }
  );

  $('.kia-carousel').on('afterChange', function (event, slick, currentSlide) {
    $('.slick-current').addClass('slide-active');
  });

  // Initialize first slide as active
  setTimeout(function () {
    $('.slick-current').addClass('slide-active');
  }, 100);
});
