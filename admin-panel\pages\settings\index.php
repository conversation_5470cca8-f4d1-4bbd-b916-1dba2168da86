<?php

/**
 * صفحة إعدادات الموقع
 * Site Settings Page
 */

// تضمين الملفات المطلوبة
require_once '../../includes/config.php';
require_once '../../includes/session.php';
require_once '../../includes/functions.php';

// التحقق من صلاحيات الإدارة
checkAdminPrivileges();

// عنوان الصفحة
$pageTitle = 'إعدادات الموقع';

$error = '';
$success = '';

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        $settings = [
            'site_name_ar' => sanitizeInput($_POST['site_name_ar'] ?? ''),
            'site_name_en' => sanitizeInput($_POST['site_name_en'] ?? ''),
            'site_url' => sanitizeInput($_POST['site_url'] ?? ''),
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'about_ar' => sanitizeInput($_POST['about_ar'] ?? ''),
            'about_en' => sanitizeInput($_POST['about_en'] ?? ''),
            'facebook' => sanitizeInput($_POST['facebook'] ?? ''),
            'instagram' => sanitizeInput($_POST['instagram'] ?? ''),
            'youtube' => sanitizeInput($_POST['youtube'] ?? ''),
            'tiktok' => sanitizeInput($_POST['tiktok'] ?? ''),
            'linkedin' => sanitizeInput($_POST['linkedin'] ?? ''),
            'map_lat' => floatval($_POST['map_lat'] ?? 0),
            'map_lng' => floatval($_POST['map_lng'] ?? 0)
        ];

        // التحقق من البيانات الأساسية
        if (empty($settings['site_name_ar'])) {
            $error = 'اسم الموقع بالعربية مطلوب';
        } elseif (!empty($settings['email']) && !validateEmail($settings['email'])) {
            $error = 'البريد الإلكتروني غير صحيح';
        } elseif (!empty($settings['site_url']) && !filter_var($settings['site_url'], FILTER_VALIDATE_URL)) {
            $error = 'رابط الموقع غير صحيح';
        } else {
            try {
                // رفع الشعار إذا تم اختياره
                $logoPath = '';
                if (isset($_FILES['logo']) && $_FILES['logo']['error'] == 0) {
                    // المسار الجديد خارج admin-panel
                    $uploadDir = '../../../assets/images/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    // تسجيل معلومات التصحيح
                    error_log("Upload directory: " . $uploadDir);
                    error_log("File info: " . print_r($_FILES['logo'], true));

                    $uploadResult = uploadFile($_FILES['logo'], $uploadDir, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp']);

                    // تسجيل نتيجة الرفع
                    error_log("Upload result: " . print_r($uploadResult, true));

                    if ($uploadResult['success']) {
                        // حذف الشعار القديم
                        $oldSettings = $db->Fetch("SELECT logo FROM settings WHERE id = 1");
                        if ($oldSettings && !empty($oldSettings['logo'])) {
                            $oldLogoPath = $uploadDir . basename($oldSettings['logo']);
                            if (file_exists($oldLogoPath)) {
                                unlink($oldLogoPath);
                            }
                        }

                        $logoPath = $uploadResult['filename'];
                    } else {
                        $error = 'فشل في رفع الشعار: ' . $uploadResult['message'];
                        error_log("Logo upload failed: " . $uploadResult['message']);
                    }
                }

                if (empty($error)) {
                    // التحقق من وجود إعدادات
                    $existingSettings = $db->Fetch("SELECT id FROM settings WHERE id = 1");

                    if ($existingSettings) {
                        // تحديث الإعدادات الموجودة
                        $updateQuery = "UPDATE settings SET 
                            site_name_ar = :site_name_ar,
                            site_name_en = :site_name_en,
                            site_url = :site_url,
                            email = :email,
                            phone = :phone,
                            about_ar = :about_ar,
                            about_en = :about_en,
                            facebook = :facebook,
                            instagram = :instagram,
                            youtube = :youtube,
                            tiktok = :tiktok,
                            linkedin = :linkedin,
                            map_lat = :map_lat,
                            map_lng = :map_lng,
                            last_update = NOW()";

                        if (!empty($logoPath)) {
                            $updateQuery .= ", logo = :logo";
                            $settings['logo'] = $logoPath;
                        }

                        $updateQuery .= " WHERE id = 1";

                        $db->Update($updateQuery, $settings);
                    } else {
                        // إنشاء إعدادات جديدة
                        $settings['logo'] = $logoPath;
                        $insertQuery = "INSERT INTO settings (
                            id, site_name_ar, site_name_en, site_url, logo, email, phone,
                            about_ar, about_en, facebook, instagram, youtube, tiktok, linkedin, map_lat, map_lng, last_update
                        ) VALUES (
                            1, :site_name_ar, :site_name_en, :site_url, :logo, :email, :phone,
                            :about_ar, :about_en, :facebook, :instagram, :youtube, :tiktok, :linkedin, :map_lat, :map_lng, NOW()
                        )";

                        $db->Insert($insertQuery, $settings);
                    }

                    $_SESSION['success_message'] = 'تم حفظ الإعدادات بنجاح';
                    header('Location: index.php');
                    exit();
                }
            } catch (Exception $e) {
                $error = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
            }
        }
    }
}

try {
    // استرجاع الإعدادات الحالية
    $currentSettings = $db->Fetch("SELECT * FROM settings WHERE id = 1");
    if (!$currentSettings) {
        // إنشاء إعدادات افتراضية
        $currentSettings = [
            'site_name_ar' => '',
            'site_name_en' => '',
            'site_url' => '',
            'logo' => '',
            'email' => '',
            'about_ar' => '',
            'about_en' => '',
            'facebook' => '',
            'instagram' => '',
            'map_lat' => 0,
            'map_lng' => 0
        ];
    }
} catch (Exception $e) {
    $error = "خطأ في استرجاع الإعدادات: " . $e->getMessage();
    $currentSettings = [];
}

// تضمين ملف الرأس
include '../../includes/header.php';
?>

<!-- محتوى صفحة إعدادات الموقع -->
<div class="container-fluid p-4">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إعدادات الموقع</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../../dashboard.php">الرئيسية</a></li>
                    <li class="breadcrumb-item active">إعدادات الموقع</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-success" onclick="$('#settingsForm').submit()">
                <i class="fas fa-save me-2"></i>
                حفظ الإعدادات
            </button>
        </div>
    </div>

    <!-- رسائل الأخطاء والنجاح -->
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نموذج الإعدادات -->
    <form method="POST" enctype="multipart/form-data" id="settingsForm">
        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

        <div class="row">
            <!-- القسم الأيسر -->
            <div class="col-lg-8">
                <!-- البيانات الأساسية -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-globe me-2"></i>
                            البيانات الأساسية للموقع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- اسم الموقع -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الموقع (بالعربية) <span class="text-danger">*</span></label>
                                <input type="text"
                                    class="form-control"
                                    name="site_name_ar"
                                    value="<?php echo htmlspecialchars($currentSettings['site_name_ar'] ?? ''); ?>"
                                    required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الموقع (بالإنجليزية)</label>
                                <input type="text"
                                    class="form-control"
                                    name="site_name_en"
                                    value="<?php echo htmlspecialchars($currentSettings['site_name_en'] ?? ''); ?>">
                            </div>

                            <!-- رابط الموقع -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رابط الموقع</label>
                                <input type="url"
                                    class="form-control"
                                    name="site_url"
                                    value="<?php echo htmlspecialchars($currentSettings['site_url'] ?? ''); ?>"
                                    placeholder="https://example.com">
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email"
                                    class="form-control"
                                    name="email"
                                    value="<?php echo htmlspecialchars($currentSettings['email'] ?? ''); ?>"
                                    placeholder="<EMAIL>">
                            </div>

                            <!-- رقم الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel"
                                    class="form-control"
                                    name="phone"
                                    value="<?php echo htmlspecialchars($currentSettings['phone'] ?? ''); ?>"
                                    placeholder="+964 ************">
                            </div>


                        </div>
                    </div>
                </div>

                <!-- نبذة عن الموقع -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            نبذة عن الموقع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">نبذة بالعربية</label>
                            <textarea class="form-control"
                                name="about_ar"
                                rows="4"
                                placeholder="اكتب نبذة عن الموقع بالعربية..."><?php echo htmlspecialchars($currentSettings['about_ar'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">نبذة بالإنجليزية</label>
                            <textarea class="form-control"
                                name="about_en"
                                rows="4"
                                placeholder="Write about the site in English..."><?php echo htmlspecialchars($currentSettings['about_en'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- وسائل التواصل الاجتماعي -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-share-alt me-2"></i>
                            وسائل التواصل الاجتماعي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fab fa-facebook text-primary me-1"></i>
                                    رابط فيسبوك
                                </label>
                                <input type="url"
                                    class="form-control"
                                    name="facebook"
                                    value="<?php echo htmlspecialchars($currentSettings['facebook'] ?? ''); ?>"
                                    placeholder="https://facebook.com/yourpage">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fab fa-instagram text-danger me-1"></i>
                                    رابط إنستغرام
                                </label>
                                <input type="url"
                                    class="form-control"
                                    name="instagram"
                                    value="<?php echo htmlspecialchars($currentSettings['instagram'] ?? ''); ?>"
                                    placeholder="https://instagram.com/yourpage">
                            </div>


                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fab fa-youtube text-danger me-1"></i>
                                    رابط اليوتيوب
                                </label>
                                <input type="url"
                                    class="form-control"
                                    name="youtube"
                                    value="<?php echo htmlspecialchars($currentSettings['youtube'] ?? ''); ?>"
                                    placeholder="https://youtube.com/yourchannel">
                            </div>


                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fab fa-tiktok text-danger me-1"></i>
                                    رابط التيكتوك
                                </label>
                                <input type="url"
                                    class="form-control"
                                    name="tiktok"
                                    value="<?php echo htmlspecialchars($currentSettings['tiktok'] ?? ''); ?>"
                                    placeholder="https://tiktok.com/@yourpage">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fab fa-linkedin text-info me-1"></i>
                                    رابط LinkedIn
                                </label>
                                <input type="url"
                                    class="form-control"
                                    name="linkedin"
                                    value="<?php echo htmlspecialchars($currentSettings['linkedin'] ?? ''); ?>"
                                    placeholder="https://linkedin.com/company/your-company">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الموقع الجغرافي -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            الموقع الجغرافي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">خط العرض (Latitude)</label>
                                <input type="number"
                                    class="form-control"
                                    name="map_lat"
                                    step="any"
                                    value="<?php echo $currentSettings['map_lat'] ?? ''; ?>"
                                    placeholder="33.3152">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">خط الطول (Longitude)</label>
                                <input type="number"
                                    class="form-control"
                                    name="map_lng"
                                    step="any"
                                    value="<?php echo $currentSettings['map_lng'] ?? ''; ?>"
                                    placeholder="44.3661">
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يمكنك الحصول على إحداثيات الموقع من
                            <a href="https://www.google.com/maps" target="_blank" class="alert-link">خرائط جوجل</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- القسم الأيمن -->
            <div class="col-lg-4">
                <!-- شعار الموقع -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-image me-2"></i>
                            شعار الموقع
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <!-- عرض الشعار الحالي -->
                        <div class="current-logo mb-3">
                            <?php if (!empty($currentSettings['logo'])): ?>
                                <img src="<?php echo '../../../assets/images/' . $currentSettings['logo']; ?>"
                                    alt="شعار الموقع"
                                    class="img-fluid rounded"
                                    style="max-height: 150px;"
                                    id="logoPreview">
                            <?php else: ?>
                                <div class="bg-light p-4 rounded">
                                    <i class="fas fa-image text-muted" style="font-size: 3rem;"></i>
                                    <p class="text-muted mt-2">لا يوجد شعار</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- رفع شعار جديد -->
                        <div class="mb-3">
                            <input type="file"
                                class="form-control image-input"
                                name="logo"
                                accept="image/*"
                                data-preview="logoPreview">
                        </div>

                        <small class="text-muted">
                            الصيغ المدعومة: JPG, PNG, GIF, SVG, WEBP<br>
                            الحد الأقصى: 5 ميجابايت
                        </small>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info me-2"></i>
                            معلومات مهمة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> هذه الإعدادات تؤثر على موقعك بالكامل
                        </div>

                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                اسم الموقع يظهر في العنوان
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                الشعار يظهر في جميع الصفحات
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                معلومات التواصل للزوار
                            </li>
                            <li>
                                <i class="fas fa-check text-success me-2"></i>
                                الموقع الجغرافي للخرائط
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="d-flex justify-content-end gap-2 mt-4">
            <button type="reset" class="btn btn-secondary">
                <i class="fas fa-undo me-2"></i>
                إعادة تعيين
            </button>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-save me-2"></i>
                حفظ الإعدادات
            </button>
        </div>
    </form>
</div>

<?php
// سكريبت مخصص للصفحة
$customScript = '
    // معاينة الشعار قبل الرفع
    $("input[name=\'logo\']").on("change", function() {
        previewImage(this, "#logoPreview");
    });

    // تأكيد قبل إعادة التعيين
    $("button[type=\'reset\']").on("click", function(e) {
        if (!confirm("هل أنت متأكد من إعادة تعيين جميع الحقول؟")) {
            e.preventDefault();
        }
    });

    // حفظ تلقائي كل دقيقة (اختياري)
    let autoSaveInterval;
    function startAutoSave() {
        autoSaveInterval = setInterval(function() {
            // حفظ البيانات في localStorage كنسخة احتياطية
            let formData = $("#settingsForm").serializeArray();
            localStorage.setItem("settings_backup", JSON.stringify(formData));
        }, 60000); // كل دقيقة
    }

    // بدء الحفظ التلقائي
    startAutoSave();

    // استرجاع النسخة الاحتياطية عند الحاجة
    function restoreBackup() {
        let backup = localStorage.getItem("settings_backup");
        if (backup && confirm("هل تريد استرجاع النسخة الاحتياطية المحفوظة؟")) {
            let formData = JSON.parse(backup);
            formData.forEach(function(field) {
                $("[name=\'"+field.name+"\'"]").val(field.value);
            });
        }
    }
';

// تضمين ملف التذييل
include '../../includes/footer.php';
?>