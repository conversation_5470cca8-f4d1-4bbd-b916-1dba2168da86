<?php
session_start();

require_once '../includes/config.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

$review_id = intval($_GET['id'] ?? 0);

if ($review_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف الرأي غير صحيح']);
    exit();
}

try {
    $review = $db->Fetch("SELECT * FROM reviews WHERE id = ?", [$review_id]);

    if (!$review) {
        echo json_encode(['success' => false, 'message' => 'الرأي غير موجود']);
        exit();
    }

    // تنسيق التواريخ
    $review['created_at'] = date('Y-m-d H:i:s', strtotime($review['created_at']));
    if ($review['purchase_date']) {
        $review['purchase_date'] = date('Y-m-d', strtotime($review['purchase_date']));
    }

    echo json_encode([
        'success' => true,
        'review' => $review
    ]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات']);
}
