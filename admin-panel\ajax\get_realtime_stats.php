<?php
// تضمين ملفات النظام المطلوبة
require_once '../includes/config.php';
require_once '../includes/session.php';
// On hosting: includes folder is outside public_html
require_once dirname(dirname(dirname(dirname(__DIR__)))) . '/includes/analytics_functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    http_response_code(403);
    exit(json_encode(['error' => 'Unauthorized']));
}

header('Content-Type: application/json');

try {
    $real_time_visitors = getRealTimeVisitors();
    
    // Get today's stats
    $today_stats = getAnalyticsData('today');
    
    echo json_encode([
        'active_visitors' => $real_time_visitors,
        'today_visits' => $today_stats['stats']['total_visits'] ?? 0,
        'today_unique' => $today_stats['stats']['unique_visitors'] ?? 0,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error']);
}